{"name": "xero-receipts-sync", "version": "1.0.0", "description": "AWS Lambda function for syncing Xero Receipts", "main": "dist/handlers/xeroReceiptsHandler.js", "type": "commonjs", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "start-offline": "serverless offline", "deploy": "serverless deploy", "prisma:generate": "npx prisma generate", "prisma:migrate": "npx prisma migrate dev", "prisma:studio": "npx prisma studio", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "format:check": "prettier --check src/**/*.ts", "type-check": "tsc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean && npm run lint && npm run type-check"}, "keywords": ["aws", "lambda", "xero", "employees", "sync", "serverless"], "author": "Your Name", "license": "MIT", "dependencies": {"@aws-sdk/client-sfn": "^3.528.0", "@aws-sdk/client-sqs": "^3.840.0", "@prisma/client": "^5.12.0", "@types/uuid": "^10.0.0", "axios": "^1.6.8", "dotenv": "^16.6.1", "moment": "^2.30.1", "uuid": "^11.1.0"}, "devDependencies": {"@types/aws-lambda": "^8.10.122", "@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.45.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "prettier": "^3.0.0", "prisma": "^5.12.0", "rimraf": "^5.0.0", "serverless": "^4.17.1", "serverless-offline": "^14.4.0", "serverless-offline-sqs": "^8.0.0", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0"}}