/**
 * Receipts Logger Utility
 *
 * This utility provides simplified logging functionality specifically for
 * Receipts Lambda function execution using only the IntegrationLog model.
 * It follows the same pattern as Accounts logging.
 *
 * Key Features:
 * - Lambda function execution logging (success/failure)
 * - Integration with existing IntegrationLog model
 * - Functional implementation
 * - Comprehensive error handling
 *
 * <AUTHOR> Logger Utility
 * @version 1.0.0
 */

import {
  logLambdaInvocation,
  updateLambdaSuccess,
  updateLambdaError,
  formatDuration,
} from './integrationLogger';

/**
 * Receipts Execution Result Interface
 */
export interface ReceiptsExecutionResult {
  totalReceipts: number;
  processedReceipts: number;
  insertedReceipts: number;
  updatedReceipts: number;
  errors: number;
  warnings: number;
  duration: string;
}

/**
 * Start Receipts Lambda execution logging
 */
export async function startReceiptsExecution(
  companyId: string,
  triggeredBy: 'USER' | 'SYSTEM' = 'SYSTEM'
): Promise<{ logId: string; startTime: number }> {
  try {
    const startTime = Date.now();
    const logId = await logLambdaInvocation(companyId, 'Receipts', triggeredBy);

    return { logId, startTime };
  } catch (error) {
    console.error('❌ Failed to start Receipts execution logging:', error);
    throw error;
  }
}

/**
 * Complete Receipts Lambda execution with success
 */
export async function completeReceiptsExecution(
  logId: string,
  startTime: number,
  result: ReceiptsExecutionResult
): Promise<void> {
  try {
    const executionTime = (Date.now() - startTime).toString();

    const summary = {
      totalReceipts: result.totalReceipts,
      processedReceipts: result.processedReceipts,
      insertedReceipts: result.insertedReceipts,
      updatedReceipts: result.updatedReceipts,
      errors: result.errors,
      warnings: result.warnings,
      executionTime: formatDuration(executionTime),
      successRate: result.totalReceipts > 0 ?
        ((result.processedReceipts - result.errors) / result.totalReceipts * 100).toFixed(2) + '%' : '0%',
    };

    await updateLambdaSuccess(logId, executionTime, "Receipts Sync successful", summary);

    console.log('📊 Receipts Execution Summary:', {
      ...summary,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('❌ Failed to complete Receipts execution logging:', error);
    // Don't throw here to avoid masking the original success
  }
}

/**
 * Complete Receipts Lambda execution with error
 */
export async function failReceiptsExecution(
  logId: string,
  startTime: number,
  error: any,
  partialResult?: Partial<ReceiptsExecutionResult>
): Promise<void> {
  try {
    const executionTime = (Date.now() - startTime).toString();

    const summary = {
      totalReceipts: partialResult?.totalReceipts || 0,
      processedReceipts: partialResult?.processedReceipts || 0,
      insertedReceipts: partialResult?.insertedReceipts || 0,
      updatedReceipts: partialResult?.updatedReceipts || 0,
      errors: (partialResult?.errors || 0) + 1, // +1 for the current error
      warnings: partialResult?.warnings || 0,
      executionTime: formatDuration(executionTime),
      errorType: error.constructor.name,
      errorMessage: error.message,
    };

    await updateLambdaError(logId, executionTime, error, summary);

    console.error('💥 Receipts Execution Failed:', {
      ...summary,
      timestamp: new Date().toISOString(),
    });
  } catch (logError) {
    console.error('❌ Failed to log Receipts execution error:', logError);
    // Don't throw here to avoid masking the original error
  }
}
