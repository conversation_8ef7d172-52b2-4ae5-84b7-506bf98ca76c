/**
 * Type definitions for Xero Bank Transactions Integration
 */

// Xero Request Data
export interface XeroRequestData {
  companyId: string;
  tenantId?: string;
  dumpToDatabase?: boolean;
  where?: string;
  page?: number;
}

// Xero Token Response
export interface XeroTokenResponse {
  id_token: string;
  access_token: string;
  expires_in: number;
  token_type: string;
  refresh_token: string;
  scope: string;
}

// Xero Bank Transaction Response
export interface XeroBankTransactionResponse {
  BankTransactions: XeroBankTransaction[];
}

// Xero Bank Transaction
export interface XeroBankTransaction {
  BankTransactionID: string;
  Type: string;
  BankAccount: {
    AccountID: string;
    Code: string;
    Name: string;
  };
  IsReconciled: boolean;
  HasAttachments: boolean;
  Contact: {
    ContactID: string;
    Name: string;
    Addresses: any[];
    Phones: any[];
    ContactGroups: any[];
    ContactPersons: any[];
    HasValidationErrors: boolean;
  };
  DateString: string;
  Date: string;
  Status: string;
  LineAmountTypes: string;
  LineItems: XeroBankTransactionLineItem[];
  SubTotal: number;
  TotalTax: number;
  Total: number;
  UpdatedDateUTC: string;
  CurrencyCode: string;
  Reference?: string;
}

// Xero Bank Transaction Line Item
export interface XeroBankTransactionLineItem {
  Description: string;
  UnitAmount: number;
  TaxType: string;
  TaxAmount: number;
  LineAmount: number;
  AccountCode: string;
  Tracking: any[];
  Quantity: number;
  LineItemID: string;
  AccountID: string;
}

// Bank Transaction Execution Result
export interface BankTransactionExecutionResult {
  totalBankTransactions: number;
  processedBankTransactions: number;
  insertedBankTransactions: number;
  updatedBankTransactions: number;
  insertedBankTransactionLines: number;
  updatedBankTransactionLines: number;
  errors: number;
  warnings: number;
  duration: string;
}

// Environment Variables Type
export interface EnvironmentConfig {
  DATABASE_URL: string;
  XERO_CLIENT_ID: string;
  XERO_CLIENT_SECRET: string;
  XERO_TOKEN_URL?: string;
  XERO_BASE_URL?: string;
  FIRST_RETRY?: string;
  SECOND_RETRY?: string;
  LAST_RETRY?: string;
  REGION?: string;
  ACCESS_KEY_ID?: string;
  SECRET_ACCESS_KEY?: string;
  IS_OFFLINE?: string;
  PRISMA_QUERY_ENGINE_LIBRARY?: string;
  AWS_LAMBDA_FUNCTION_NAME?: string;
}

// Custom Error Types
export class XeroError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public originalError?: any
  ) {
    super(message);
    this.name = 'XeroError';
  }
}

export class TokenRefreshError extends XeroError {
  constructor(message: string, originalError?: any) {
    super(message, 401, originalError);
    this.name = 'TokenRefreshError';
  }
}

export class ValidationError extends Error {
  constructor(
    message: string,
    public missingFields?: string[]
  ) {
    super(message);
    this.name = 'ValidationError';
  }
}

// Utility Types
export type NonNullable<T> = T extends null | undefined ? never : T;

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
