// Request/Response Types
export interface XeroRequestData {
  userId: string;
  companyId: string;
  startDate: string;
  endDate: string;

  dumpToDatabase?: boolean;
  where?: string;
}

export interface XeroTokenResponse {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  token_type: string;
}

// Xero Receipt API Response Types
export interface XeroReceipt {
  ReceiptID: string;
  ReceiptNumber?: string;
  Status?: string;
  UserID?: string;
  FirstName?: string;
  LastName?: string;
  ContactID?: string;
  ContactName?: string;
  Date?: string;
  Reference?: string;
  LineAmountTypes?: string;
  SubTotal?: number;
  TotalTax?: number;
  Total?: number;
  HasAttachments?: boolean;
  UpdatedDateUTC: string;
}

export interface XeroReceiptsResponse {
  Receipts: XeroReceipt[];
}

export interface XeroApiResponse {
  Receipts?: XeroReceipt[];
}

// Internal Processing Types
export interface ProcessedReceiptData {
  ReceiptID: string;
  ReceiptNumber?: number | string;
  Status?: string;
  UpdatedDateUTC?: string;
  Date?: string;
  Reference?: string;
  LineAmountTypes?: string;
  SubTotal?: number;
  TotalTax?: number;
  Total?: number;
  HasAttachments?: boolean;
  User?: {
    UserID?: string;
    FirstName?: string;
    LastName?: string;
  };
  Contact?: {
    ContactID?: string;
    Name?: string;
  };
};

// Environment Variables Type
export interface EnvironmentConfig {
  DATABASE_URL: string;
  XERO_CLIENT_ID: string;
  XERO_CLIENT_SECRET: string;
  XERO_TOKEN_URL?: string;
  XERO_BASE_URL?: string;
  FIRST_RETRY?: string;
  SECOND_RETRY?: string;
  LAST_RETRY?: string;
  REGION?: string;
  ACCESS_KEY_ID?: string;
  SECRET_ACCESS_KEY?: string;
  IS_OFFLINE?: string;
  PRISMA_QUERY_ENGINE_LIBRARY?: string;
  AWS_LAMBDA_FUNCTION_NAME?: string;
}

// Custom Error Types
export class XeroError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public originalError?: any
  ) {
    super(message);
    this.name = 'XeroError';
  }
}

export class TokenRefreshError extends XeroError {
  constructor(message: string, originalError?: any) {
    super(message, 401, originalError);
    this.name = 'TokenRefreshError';
  }
}

export class ValidationError extends Error {
  constructor(
    message: string,
    public missingFields?: string[]
  ) {
    super(message);
    this.name = 'ValidationError';
  }
}

// Utility Types
export type NonNullable<T> = T extends null | undefined ? never : T;

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
