/**
 * Integration Logger Utility for Accounts Service
 *
 * This utility provides comprehensive logging functionality for Accounts
 * synchronization operations using the IntegrationLog model.
 *
 * Key Features:
 * - Lambda function execution logging (success/failure)
 * - API call tracking with detailed metrics
 * - Sync operation monitoring with status management
 * - Error handling and diagnostic information
 * - Performance metrics and timing data
 *
 * <AUTHOR> Integration Logger
 * @version 1.0.0
 */

import { PrismaClient } from '@prisma/client';
import { v4 as uuidv4 } from 'uuid';

// Prisma client singleton
let prisma: PrismaClient | null = null;
function getPrismaClient(): PrismaClient {
  if (!prisma) {
    const config = {
      log: ['error', 'warn'] as Array<'error' | 'warn'>,
      errorFormat: 'pretty' as const,
    };
    if (process.env['IS_OFFLINE'] === 'true') {
      delete process.env['PRISMA_QUERY_ENGINE_LIBRARY'];
    }
    prisma = new PrismaClient(config);
  }
  return prisma;
}

/**
 * Sync Status Enum
 */
export enum SyncStatus {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  SUCCESS = 'SUCCESS',
  ERROR = 'ERROR',
  CANCELLED = 'CANCELLED',
}

/**
 * Integration Log Data Interface
 */
export interface IntegrationLogData {
  requestId?: string;
  companyId: string;
  apiName: string;
  method?: string;
  apiUrl?: string;
  integrationName?: string;
  statusCode?: string;
  duration?: string;
  message?: string;
  entity?: string;
  triggeredBy?: 'USER' | 'SYSTEM';
  syncStatus?: SyncStatus;
  apiRequest?: any;
  apiResponse?: any;
  errorDetails?: any;
  startedAt?: Date;
  completedAt?: Date;
  syncSummary?: any;
}

/**
 * Lambda Execution Context
 */
export interface LambdaExecutionContext {
  requestId: string;
  companyId: string;
  entity: string;
  triggeredBy: 'USER' | 'SYSTEM';
  startTime: number;
}

/**
 * Format duration from milliseconds to human readable string
 */
export function formatDuration(durationMs: string): string {
  const duration = parseInt(durationMs);
  if (duration < 1000) {
    return `${duration}ms`;
  } else if (duration < 60000) {
    return `${(duration / 1000).toFixed(1)}s`;
  } else {
    const minutes = Math.floor(duration / 60000);
    const seconds = Math.floor((duration % 60000) / 1000);
    return `${minutes}m ${seconds}s`;
  }
}

/**
 * Create a new integration log entry
 */
export async function createIntegrationLog(logData: IntegrationLogData): Promise<string> {
  try {
    const integrationLog = await getPrismaClient().integrationLog.create({
      data: {
        Id: uuidv4(),
        RequestId: logData.requestId || uuidv4(),
        CompanyId: logData.companyId,
        ApiName: logData.apiName,
        Method: logData.method || null,
        ApiUrl: logData.apiUrl || null,
        IntegrationName: logData.integrationName || 'Xero',
        StatusCode: logData.statusCode || null,
        Duration: logData.duration?.toString() || null,
        Message: logData.message || null,
        Entity: logData.entity || null,
        TriggeredBy: logData.triggeredBy || 'SYSTEM',
        SyncStatus: logData.syncStatus || SyncStatus.PENDING,
        ApiRequest: logData.apiRequest || null,
        ApiResponse: logData.apiResponse || null,
        ErrorDetails: logData.errorDetails || null,
        StartedAt: logData.startedAt || new Date(),
        CompletedAt: logData.completedAt || null,
        SyncSummary: logData.syncSummary || null,
        UpdatedAt: new Date(),
      },
    });

    return integrationLog.Id;
  } catch (error) {
    console.error('❌ Failed to create integration log:', error);
    throw error;
  }
}

/**
 * Update an existing integration log entry
 */
export async function updateIntegrationLog(
  logId: string,
  updateData: Partial<IntegrationLogData>
): Promise<void> {
  try {
    const updatePayload: any = {};

    if (updateData.statusCode !== undefined) updatePayload.StatusCode = updateData.statusCode;
    if (updateData.duration !== undefined) updatePayload.Duration = updateData.duration;
    if (updateData.message !== undefined) updatePayload.Message = updateData.message;
    if (updateData.syncStatus !== undefined) updatePayload.SyncStatus = updateData.syncStatus;
    if (updateData.apiRequest !== undefined) updatePayload.ApiRequest = updateData.apiRequest;
    if (updateData.apiResponse !== undefined) updatePayload.ApiResponse = updateData.apiResponse;
    if (updateData.errorDetails !== undefined) updatePayload.ErrorDetails = updateData.errorDetails;
    if (updateData.completedAt !== undefined) updatePayload.CompletedAt = updateData.completedAt;
    if (updateData.syncSummary !== undefined) updatePayload.SyncSummary = updateData.syncSummary;

    // Always update the UpdatedAt timestamp
    updatePayload.UpdatedAt = new Date();

    await getPrismaClient().integrationLog.update({
      where: { Id: logId },
      data: updatePayload,
    });
  } catch (error) {
    console.error('❌ Failed to update integration log:', error);
    throw error;
  }
}

/**
 * Log Lambda invocation - creates initial log entry that will be updated on completion
 */
export async function logLambdaInvocation(
  companyId: string,
  lambdaName: string,
  triggeredBy: 'USER' | 'SYSTEM' = 'SYSTEM'
): Promise<string> {
  const requestId = uuidv4();
  const logData: IntegrationLogData = {
    requestId,
    companyId,
    apiName: lambdaName,
    method: 'LAMBDA',
    apiUrl: `/lambda/${lambdaName.toLowerCase()}`,
    integrationName: 'Xero',
    message: `${lambdaName} Lambda invocation started`,
    entity: lambdaName,
    triggeredBy,
    syncStatus: SyncStatus.IN_PROGRESS,
    startedAt: new Date(),
  };

  console.log(`🚀 ${lambdaName} Lambda invocation started for company: ${companyId}`);
  return await createIntegrationLog(logData);
}

/**
 * Update Lambda invocation log with success status
 */
export async function updateLambdaSuccess(
  logId: string,
  duration: string,
  message: string,
  syncSummary?: any
): Promise<void> {
  const updateData: Partial<IntegrationLogData> = {
    statusCode: '200',
    duration,
    message,
    syncStatus: SyncStatus.SUCCESS,
    syncSummary,
    completedAt: new Date(),
  };

  await updateIntegrationLog(logId, updateData);
  console.log(`✅ Lambda execution completed successfully: ${message}`);
}

/**
 * Update Lambda invocation log with error status
 */
export async function updateLambdaError(
  logId: string,
  duration: string,
  error: Error,
  partialSummary?: any
): Promise<void> {
  const errorDetails = {
    message: error.message,
    stack: error.stack,
    name: error.name,
  };

  const updateData: Partial<IntegrationLogData> = {
    statusCode: '500',
    duration,
    message: `Bank Transfers sync failed: ${error.message}`,
    syncStatus: SyncStatus.ERROR,
    errorDetails,
    syncSummary: partialSummary,
    completedAt: new Date(),
  };

  await updateIntegrationLog(logId, updateData);
  console.error(`❌ Lambda execution failed: ${error.message}`);
}
