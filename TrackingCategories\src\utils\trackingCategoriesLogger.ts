/**
 * Tracking Categories Logger Utility
 *
 * This utility provides logging functionality for
 * Tracking Categories Lambda function execution using the IntegrationLog model.
 *
 * Key Features:
 * - Lambda function execution logging (success/failure)
 * - Integration with existing IntegrationLog model
 * - Functional implementation
 * - Comprehensive error handling
 *
 * <AUTHOR> Categories Logger Utility
 * @version 1.0.0
 */

import {
  logLambdaInvocation,
  updateLambdaSuccess,
  updateLambdaError,
  formatDuration,
} from './integrationLogger';

/**
 * Tracking Categories Execution Result Interface
 */
export interface TrackingCategoriesExecutionResult {
  totalTrackingCategories: number;
  processedTrackingCategories: number;
  insertedTrackingCategories: number;
  updatedTrackingCategories: number;
  errors: number;
  warnings: number;
  duration: string;
}

/**
 * Start Tracking Categories Lambda execution logging
 */
export async function startTrackingCategoriesExecution(
  companyId: string,
  triggeredBy: 'USER' | 'SYSTEM' = 'SYSTEM'
): Promise<{ logId: string; startTime: number }> {
  try {
    const startTime = Date.now();
    const logId = await logLambdaInvocation(companyId, 'TrackingCategories', triggeredBy);
    return { logId, startTime };
  } catch (error) {
    console.error('❌ Failed to start Tracking Categories execution logging:', error);
    throw error;
  }
}

/**
 * Log Tracking Categories execution success
 */
export async function logTrackingCategoriesSuccess(
  logId: string,
  startTime: number,
  result: TrackingCategoriesExecutionResult
): Promise<void> {
  try {
    const duration = formatDuration(Date.now() - startTime);
    const message = `Tracking Categories sync successful. Processed ${result.processedTrackingCategories} tracking categories (${result.insertedTrackingCategories} inserted, ${result.updatedTrackingCategories} updated).`;
    const syncSummary = {
      totalTrackingCategories: result.totalTrackingCategories,
      processedTrackingCategories: result.processedTrackingCategories,
      insertedTrackingCategories: result.insertedTrackingCategories,
      updatedTrackingCategories: result.updatedTrackingCategories,
      duration: result.duration,
      errors: result.errors,
      warnings: result.warnings,
    };
    await updateLambdaSuccess(logId, duration, message, syncSummary);
  } catch (logError) {
    console.error('❌ Failed to log Tracking Categories success:', logError);
    // Don't throw here to avoid masking the original success
  }
}

/**
 * Log Tracking Categories execution failure
 */
export async function logTrackingCategoriesFailure(
  logId: string,
  startTime: number,
  error: Error,
  partialResult?: Partial<TrackingCategoriesExecutionResult>
): Promise<void> {
  try {
    const duration = formatDuration(Date.now() - startTime);
    const partialSummary = {
      totalTrackingCategories: partialResult?.totalTrackingCategories || 0,
      processedTrackingCategories: partialResult?.processedTrackingCategories || 0,
      insertedTrackingCategories: partialResult?.insertedTrackingCategories || 0,
      updatedTrackingCategories: partialResult?.updatedTrackingCategories || 0,
      duration: partialResult?.duration || duration,
      errors: 1,
      warnings: partialResult?.warnings || 0,
    };
    await updateLambdaError(logId, duration, error, partialSummary);
  } catch (logError) {
    console.error('❌ Failed to log Tracking Categories failure:', logError);
    // Don't throw here to avoid masking the original error
  }
}

/**
 * Wrapper function for complete Tracking Categories execution with logging
 */
export async function executeWithTrackingCategoriesLogging<T>(
  companyId: string,
  operation: () => Promise<T>,
  createResult: (data: T, duration: string) => TrackingCategoriesExecutionResult,
  triggeredBy: 'USER' | 'SYSTEM' = 'SYSTEM'
): Promise<T> {
  const startTime = Date.now();
  let logId: string | null = null;
  try {
    // Start execution logging
    const logInfo = await startTrackingCategoriesExecution(companyId, triggeredBy);
    logId = logInfo.logId;
    // Execute the operation
    const result = await operation();
    // Calculate duration
    const duration = formatDuration(Date.now() - startTime);
    // Create execution result
    const executionResult = createResult(result, duration);
    // Log success
    await logTrackingCategoriesSuccess(logId, startTime, executionResult);
    return result;
  } catch (error) {
    // Calculate duration
    const duration = formatDuration(Date.now() - startTime);
    // Log failure
    if (logId) {
      await logTrackingCategoriesFailure(logId, startTime, error as Error, {
        duration,
        errors: 1,
      });
    }
    throw error;
  }
} 