/**
 * Xero Bank Transfers Data Synchronization Handler
 *
 * This AWS Lambda handler synchronizes Bank Transfer data from Xero API.
 *
 * Key Features:
 * - Bulk insert and update of bank transfers
 * - Handles both API Gateway and SQS events
 * - Automatic Xero token refresh
 * - Comprehensive error handling and logging
 * - Incremental sync support
 *
 * Usage:
 * - API Gateway: POST /xero/sync-bank-transfers with { companyId: "uuid" }
 * - SQS: Queue message with { companyId: "uuid", tenantId?: "string" }
 *
 * Environment Variables Required:
 * - DATABASE_URL: PostgreSQL connection string
 * - XERO_CLIENT_ID: Xero OAuth client ID
 * - XERO_CLIENT_SECRET: Xero OAuth client secret
 * - XERO_BASE_URL: Xero API base URL (optional, defaults to production)
 */
import { APIGatewayProxyEvent, Context, APIGatewayProxyResult, SQSEvent } from 'aws-lambda';
import { PrismaClient, Company } from '@prisma/client';

import axios from '../utils/axiosInstance';
import { refreshXeroToken } from '../services/refreshTokenService';
import { getXeroConfig } from '../config/environment';
import { XeroRequestData, ValidationError, XeroError, ProcessedBankTransferData } from '../types';
import {
    startBankTransfersExecution,
    completeBankTransfersExecution,
    failBankTransfersExecution,
    BankTransfersExecutionResult,
} from '../utils/bankTransfersLogger';
import {
    parseXeroDate,
    getCurrentSyncDate,
    generateXeroDateTime,
    formatDateForLogging,
} from '../utils/dateUtils';

// Production Configuration
const PRODUCTION_CONFIG = {
    API_TIMEOUT_MS: 30000,
    MAX_RETRIES: 3,
    RETRY_DELAY_MS: 1000,
};

// Get Prisma client
function getPrismaClient(): PrismaClient {
    return new PrismaClient({
        log: ['error', 'warn'],
        errorFormat: 'pretty',
    });
}

// Lambda handler
export const handler = async (
    event: APIGatewayProxyEvent | SQSEvent,
    context: Context
): Promise<APIGatewayProxyResult | void> => {
    if ('Records' in event && event.Records?.length) {
        // Handle SQS event - batch processing
        for (const record of event.Records) {
            try {
                const requestData = JSON.parse(record.body);
                await processRequest(requestData, context, 'SYSTEM');
            } catch (err) {
                console.error('❌ Failed to process SQS message', err);
                // Re-throw error to trigger SQS retry mechanism
                throw err;
            }
        }
        return;
    } else {
        // Handle API Gateway event - direct HTTP request
        try {
            const requestData = parseRequestData(event);
            await processRequest(requestData, context, 'USER');

            return {
                statusCode: 200,
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    success: true,
                    message: 'Bank Transfers data processed successfully',
                    timestamp: new Date().toISOString(),
                }),
            };
        } catch (err: any) {
            console.error('❌ Handler error:', err);
            return {
                statusCode: err instanceof ValidationError ? 400 : 500,
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    success: false,
                    error: err.message,
                    type: err.constructor.name,
                }),
            };
        }
    }
};

// parse event
export function parseRequestData(
    event: APIGatewayProxyEvent | SQSEvent | { body?: string }
): XeroRequestData {
    // handle SQS
    if ('Records' in event && event.Records?.[0]) {
        try {
            const parsed = JSON.parse(event.Records[0].body);
            return parsed as XeroRequestData;
        } catch {
            throw new ValidationError('Failed to parse SQS message body');
        }
    }

    // handle API Gateway
    if ('body' in event && event.body) {
        try {
            const parsed = typeof event.body === 'string' ? JSON.parse(event.body) : event.body;
            return parsed as XeroRequestData;
        } catch {
            throw new ValidationError('Failed to parse API Gateway body');
        }
    }

    throw new ValidationError('Invalid request data');
}

// process request
async function processRequest(
    requestData: XeroRequestData,
    _context: Context,
    triggeredBy: 'USER' | 'SYSTEM' = 'SYSTEM'
): Promise<void> {
    const startTime = Date.now();
    let integrationLogId: string | null = null;
    let recordsProcessed = 0;
    let recordsInserted = 0;
    let recordsUpdated = 0;

    try {
        // Start execution logging
        const { logId } = await startBankTransfersExecution(requestData.companyId, triggeredBy);
        integrationLogId = logId;

        validateRequestData(requestData);

        const prisma = getPrismaClient();
        const integration = await getActiveIntegration(requestData, prisma);

        if (!integration) {
            throw new Error('Active integration not found');
        }

        const validIntegration = await ensureValidToken(integration);

        // Get last sync date for incremental sync

        const lastSyncDate = await getLastSyncDate(requestData.companyId, prisma);
        const syncStartTime = getCurrentSyncDate();


        console.log(`🚀 Fetching Bank Transfers for company: ${requestData.companyId}`, lastSyncDate);

        // Create request data with UpdatedDateUTC filter for incremental sync
        if (lastSyncDate != null) {
            requestData = {
                ...requestData,
                where: `CreatedDateUTC>=${generateXeroDateTime(lastSyncDate)}`
            };
        }

        // Fetch bank transfers from Xero with incremental sync
        const bankTransfersData = await getBankTransfers(
            validIntegration.XeroAccessToken!,
            validIntegration.XeroTenantId!,
            requestData
        );

        if (bankTransfersData && bankTransfersData.length > 0) {
            if (requestData.dumpToDatabase !== false) {
                const saveResult = await saveBankTransferToDatabase(
                    bankTransfersData,
                    requestData.companyId,
                    prisma
                );
                recordsProcessed = bankTransfersData.length;
                recordsInserted = saveResult.inserted;
                recordsUpdated = saveResult.updated;

                console.log(
                    `✅ Successfully processed ${recordsProcessed} Bank Transfers (${recordsInserted} inserted, ${recordsUpdated} updated)`
                );
            } else {
                console.log('📊 Data fetched but not dumped to DB (dumpToDatabase is false)');
                recordsProcessed = bankTransfersData.length;
            }
        } else {
            console.log('📭 No bank transfers found to process');
        }

        // Update sync timestamp
        if (requestData.dumpToDatabase !== false) {
            await updateLastSyncDate(requestData.companyId, syncStartTime, prisma);
        }

        // Complete execution logging
        if (integrationLogId) {
            const result: BankTransfersExecutionResult = {
                totalBankTransfers: recordsProcessed,
                processedBankTransfers: recordsProcessed,
                insertedBankTransfers: recordsInserted,
                updatedBankTransfers: recordsUpdated,
                errors: 0,
                warnings: 0,
                duration: `${Date.now() - startTime}ms`,
            };
            await completeBankTransfersExecution(integrationLogId, startTime, result);
        }

    } catch (error: any) {
        console.error('❌ Process request error:', error);

        // Log execution failure
        if (integrationLogId) {
            const partialResult: Partial<BankTransfersExecutionResult> = {
                totalBankTransfers: recordsProcessed,
                processedBankTransfers: recordsProcessed,
                insertedBankTransfers: recordsInserted,
                updatedBankTransfers: recordsUpdated,
                errors: 1,
                warnings: 0,
            };
            await failBankTransfersExecution(integrationLogId, startTime, error, partialResult);
        }

        throw error;
    }
}

// Validate request data
function validateRequestData(data: XeroRequestData): void {
    if (!data.companyId) {
        throw new ValidationError('companyId is required');
    }
}

// Get active integration
async function getActiveIntegration(data: XeroRequestData, prisma: PrismaClient): Promise<Company> {
    const integration = await prisma.company.findFirst({
        where: {
            Id: data.companyId,
        },
    });

    if (!integration) {
        throw new Error('Active integration not found for company');
    }

    return integration;
}

// Ensure valid token
async function ensureValidToken(integration: Company): Promise<Company> {
    if (!integration.XeroAccessToken) {
        throw new Error('No Xero access token found');
    }

    // Check if token is expired (with 5-minute buffer)
    const now = new Date();
    const tokenExpiry = integration.XeroTokenExpiry;
    const bufferTime = 5 * 60 * 1000; // 5 minutes in milliseconds

    if (!tokenExpiry || now.getTime() > (tokenExpiry.getTime() - bufferTime)) {
        console.log('🔄 Token expired or expiring soon, refreshing...');
        return await refreshXeroToken(integration);
    }

    return integration;
}

// Get last sync date for incremental sync
async function getLastSyncDate(companyId: string, prisma: PrismaClient): Promise<Date | null> {
    try {
        const syncRecord = await prisma.xeroModuleSync.findUnique({
            where: {
                CompanyId_ModuleName: {
                    CompanyId: companyId,
                    ModuleName: 'BankTransfers',
                },
            },
        });

        return syncRecord?.LastSyncTime || null;
    } catch (error) {
        console.error('❌ Error getting last sync date:', error);
        return null;
    }
}

// Update last sync date

async function updateLastSyncDate(companyId: string, syncDate: Date, prisma: PrismaClient): Promise<void> {
    try {
        await prisma.xeroModuleSync.upsert({
            where: {
                CompanyId_ModuleName: {
                    CompanyId: companyId,
                    ModuleName: 'BankTransfers'
                }
            },
            update: {
                LastSyncTime: syncDate,
                UpdatedAt: getCurrentSyncDate()
            },
            create: {
                Id: require('uuid').v4(),
                CompanyId: companyId,
                ModuleName: 'BankTransfers',
                LastSyncTime: syncDate,
                CreatedAt: getCurrentSyncDate(),
                UpdatedAt: getCurrentSyncDate()
            }
        });

        console.log(`📅 Updated last sync date to: ${formatDateForLogging(syncDate)}`);
    } catch (error) {
        console.error("Error updating last sync date:", error);
        // Don't throw here as this shouldn't fail the main sync process
    }
}
// Get bank transfers from Xero with improved error handling and timeout configuration
const getBankTransfers = async (
    accessToken: string,
    tenantId: string,
    requestData: XeroRequestData
): Promise<any[]> => {
    const startTime = Date.now();

    try {
        const config = getXeroConfig();
        const url = `${config.baseUrl}BankTransfers`;

        console.log(`Making GET request to ${url}`);
        const response = await axios.get(url, {
            headers: {
                Authorization: `Bearer ${accessToken}`,
                'Xero-tenant-id': tenantId,
                Accept: 'application/json',
                'User-Agent': 'BankTransfersSync/1.0.0',
            },
            params: requestData.where ? { where: requestData.where } : {},
            timeout: PRODUCTION_CONFIG.API_TIMEOUT_MS, // Configurable API timeout
        });

        const requestTime = Date.now() - startTime;
        console.log(`✅ BankTransfers API call completed in ${requestTime}ms`);

        const BankTransfersData = response?.data;
        console.log('Xero BankTransfers API raw response:', JSON.stringify(response.data, null, 2));

        if (!BankTransfersData || !BankTransfersData.BankTransfers) {
            throw new Error('Invalid BankTransfers data structure received from Xero');
        }

        console.log(`📊 Retrieved ${BankTransfersData.BankTransfers.length} BankTransfers from Xero`);
        return BankTransfersData.BankTransfers;
    } catch (error: any) {
        console.error(error);
        const requestTime = Date.now() - startTime;
        console.error(`❌ BankTransfers API call failed after ${requestTime}ms:`, {
            error: error.message,
            status: error.response?.status,
            statusText: error.response?.statusText,
            tenantId: tenantId.substring(0, 8) + '...',
        });

        // Handle specific Xero API errors
        if (error.response?.status === 429) {
            throw new XeroError(`Rate limit exceeded. Please try again later.`, 429, error);
        }

        if (error.response?.status === 401) {
            throw new XeroError(`Authentication failed. Token may be expired.`, 401, error);
        }

        if (error.response?.status === 403) {
            throw new XeroError(`Access forbidden. Check your Xero permissions.`, 403, error);
        }

        if (error.response?.status === 404) {
            throw new XeroError(`BankTransfers endpoint not found.`, 404, error);
        }

        if (error.code === 'ECONNABORTED') {
            throw new XeroError(`Request timeout after ${PRODUCTION_CONFIG.API_TIMEOUT_MS}ms`, 408, error);
        }

        // Generic error
        throw new XeroError(
            `Failed to fetch BankTransfers: ${error.message}`,
            error.response?.status || 500,
            error
        );
    }
};

// Save bank transfers to database
async function saveBankTransferToDatabase(
    bankTransfers: any[],
    companyId: string,
    prisma: PrismaClient
): Promise<{ inserted: number; updated: number }> {
    console.log(`Saving ${bankTransfers.length} bank transfers to database`);

    // 1. Map all bank transfers to DB shape
    const bankTransferRecords: any[] = bankTransfers.map(transfer => ({
        BankTransferID: transfer.BankTransferID,
        FromBankAccountAccountID: transfer.FromBankAccount?.AccountID || null,
        FromBankAccountName: transfer.FromBankAccount?.Name || null,
        ToBankAccountAccountID: transfer.ToBankAccount?.AccountID || null,
        ToBankAccountName: transfer.ToBankAccount?.Name || null,
        Amount: transfer.Amount || null,
        FromBankTransactionID: transfer?.FromBankTransactionID || null,
        ToBankTransactionID: transfer.ToBankTransactionID || null,
        FromIsReconciled: transfer.FromIsReconciled || null,
        ToIsReconciled: transfer.ToIsReconciled || null,
        CurrencyRate: transfer.CurrencyRate || null,
        Reference: transfer.Reference || null,
        CreatedDateUTC: parseXeroDate(transfer.CreatedDateUTC) || null,
        UpdateUTCDate: parseXeroDate(transfer.UpdatedDateUTC) || new Date(),
        CompanyId: companyId,
    }));
    const bankTransferIds = bankTransferRecords.map(bt => bt.BankTransferID);

    // 2. Fetch existing BankTransferIDs
    const existingBankTransfers = await prisma.bankTransfer.findMany({
        where: { BankTransferID: { in: bankTransferIds } },
        select: { BankTransferID: true },
    });
    const existingBankTransferIds = new Set(existingBankTransfers.map(bt => bt.BankTransferID));

    // 3. Split into new and existing
    const bankTransfersToInsert = bankTransferRecords.filter(bt => !existingBankTransferIds.has(bt.BankTransferID));
    const bankTransfersToUpdate = bankTransferRecords.filter(bt => existingBankTransferIds.has(bt.BankTransferID));

    // 4. Bulk insert new bank transfers
    if (bankTransfersToInsert.length > 0) {
        await prisma.bankTransfer.createMany({ data: bankTransfersToInsert, skipDuplicates: true });
        console.log(`📥 Inserted ${bankTransfersToInsert.length} new bank transfers`);
    }

    // 5. Bulk update existing bank transfers
    if (bankTransfersToUpdate.length > 0) {
        await prisma.$transaction(
            bankTransfersToUpdate.map(bankTransfer =>
                prisma.bankTransfer.update({
                    where: { BankTransferID: bankTransfer.BankTransferID },
                    data: bankTransfer,
                })
            )
        );
        console.log(`🔄 Updated ${bankTransfersToUpdate.length} existing bank transfers`);
    }

    return {
        inserted: bankTransfersToInsert.length,
        updated: bankTransfersToUpdate.length,
    };
}
