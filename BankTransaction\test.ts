/**
 * Simple test file to verify BankTransaction lambda function structure
 */

import { handler } from './src/handlers/xeroBankTransactionsHandler';

// Test event for API Gateway
const testEvent = {
    body: JSON.stringify({
        companyId: 'test-company-id',
        dumpToDatabase: false, // Set to false for testing
    }),
    headers: {},
    multiValueHeaders: {},
    httpMethod: 'POST',
    isBase64Encoded: false,
    path: '/xero/sync-banktransactions',
    pathParameters: null,
    queryStringParameters: null,
    multiValueQueryStringParameters: null,
    stageVariables: null,
    requestContext: {} as any,
    resource: '',
};

// Test context
const testContext = {
    callbackWaitsForEmptyEventLoop: false,
    functionName: 'test-function',
    functionVersion: '1',
    invokedFunctionArn: 'test-arn',
    memoryLimitInMB: '128',
    awsRequestId: 'test-request-id',
    logGroupName: 'test-log-group',
    logStreamName: 'test-log-stream',
    getRemainingTimeInMillis: () => 30000,
    done: () => {},
    fail: () => {},
    succeed: () => {},
};

console.log('🧪 Testing BankTransaction lambda function structure...');
console.log('✅ Handler imported successfully');
console.log('✅ Test event and context created');
console.log('🎉 BankTransaction lambda function structure is valid!');

export { handler };
