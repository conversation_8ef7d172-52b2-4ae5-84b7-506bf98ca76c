/**
 * Bank Transfers Logger Utility
 *
 * This utility provides simplified logging functionality specifically for
 * Bank Transfers Lambda function execution using only the IntegrationLog model.
 * It follows the same pattern as Accounts logging.
 *
 * Key Features:
 * - Lambda function execution logging (success/failure)
 * - Integration with existing IntegrationLog model
 * - Functional implementation
 * - Comprehensive error handling
 *
 * <AUTHOR> Transfers Logger Utility
 * @version 1.0.0
 */

import {
  logLambdaInvocation,
  updateLambdaSuccess,
  updateLambdaError,
  formatDuration,
} from './integrationLogger';

/**
 * Bank Transfers Execution Result Interface
 */
export interface BankTransfersExecutionResult {
  totalBankTransfers: number;
  processedBankTransfers: number;
  insertedBankTransfers: number;
  updatedBankTransfers: number;
  errors: number;
  warnings: number;
  duration: string;
}

/**
 * Start Bank Transfers Lambda execution logging
 */
export async function startBankTransfersExecution(
  companyId: string,
  triggeredBy: 'USER' | 'SYSTEM' = 'SYSTEM'
): Promise<{ logId: string; startTime: number }> {
  try {
    const startTime = Date.now();
    const logId = await logLambdaInvocation(companyId, 'BankTransfers', triggeredBy);

    return { logId, startTime };
  } catch (error) {
    console.error('❌ Failed to start Bank Transfers execution logging:', error);
    throw error;
  }
}

/**
 * Complete Bank Transfers Lambda execution with success
 */
export async function completeBankTransfersExecution(
  logId: string,
  startTime: number,
  result: BankTransfersExecutionResult
): Promise<void> {
  try {
    const executionTime = (Date.now() - startTime).toString();

    const summary = {
      totalBankTransfers: result.totalBankTransfers,
      processedBankTransfers: result.processedBankTransfers,
      insertedBankTransfers: result.insertedBankTransfers,
      updatedBankTransfers: result.updatedBankTransfers,
      errors: result.errors,
      warnings: result.warnings,
      executionTime: formatDuration(executionTime),
      successRate: result.totalBankTransfers > 0 ?
        ((result.processedBankTransfers - result.errors) / result.totalBankTransfers * 100).toFixed(2) + '%' : '0%',
    };

    await updateLambdaSuccess(logId, executionTime, "Bank Transfers Sync successful", summary);

    console.log('📊 Bank Transfers Execution Summary:', {
      ...summary,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('❌ Failed to complete Bank Transfers execution logging:', error);
    // Don't throw here to avoid masking the original success
  }
}

/**
 * Complete Bank Transfers Lambda execution with error
 */
export async function failBankTransfersExecution(
  logId: string,
  startTime: number,
  error: any,
  partialResult?: Partial<BankTransfersExecutionResult>
): Promise<void> {
  try {
    const executionTime = (Date.now() - startTime).toString();

    const summary = {
      totalBankTransfers: partialResult?.totalBankTransfers || 0,
      processedBankTransfers: partialResult?.processedBankTransfers || 0,
      insertedBankTransfers: partialResult?.insertedBankTransfers || 0,
      updatedBankTransfers: partialResult?.updatedBankTransfers || 0,
      errors: (partialResult?.errors || 0) + 1, // +1 for the current error
      warnings: partialResult?.warnings || 0,
      executionTime: formatDuration(executionTime),
      errorType: error.constructor.name,
      errorMessage: error.message,
    };

    await updateLambdaError(logId, executionTime, error, summary);

    console.error('💥 Bank Transfers Execution Failed:', {
      ...summary,
      timestamp: new Date().toISOString(),
    });
  } catch (logError) {
    console.error('❌ Failed to log Bank Transfers execution error:', logError);
    // Don't throw here to avoid masking the original error
  }
}
