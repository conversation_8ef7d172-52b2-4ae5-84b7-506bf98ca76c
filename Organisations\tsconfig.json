{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictBindCallApply": true, "strictPropertyInitialization": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "noImplicitOverride": true, "exactOptionalPropertyTypes": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "dist", "rootDir": "src", "removeComments": true, "noEmitOnError": true, "preserveConstEnums": true, "types": ["node", "aws-lambda"], "typeRoots": ["node_modules/@types", "src/types"], "resolveJsonModule": true, "skipLibCheck": true, "moduleResolution": "node", "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/types/*": ["src/types/*"], "@/utils/*": ["src/utils/*"], "@/services/*": ["src/services/*"], "@/handlers/*": ["src/handlers/*"], "@/config/*": ["src/config/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", ".serverless", "dist", "**/*.test.ts", "**/*.spec.ts"], "ts-node": {"esm": true}}