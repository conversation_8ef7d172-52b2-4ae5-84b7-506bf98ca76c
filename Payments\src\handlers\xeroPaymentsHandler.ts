/*
 * Xero Payments Data Synchronization Handler
 *
 * This AWS Lambda handler synchronizes Payment data from Xero API.
 *
 * Key Features:
 * - Bulk insert and update of payments
 * - Handles both API Gateway and SQS events
 * - Automatic Xero token refresh
 * - Comprehensive error handling and logging
 *
 * Usage:
 * - API Gateway: POST /payments/sync with { companyId: "uuid" }
 * - SQS: Queue message with { companyId: "uuid", tenantId?: "string" }
 *
 * Environment Variables Required:
 * - DATABASE_URL: PostgreSQL connection string
 * - XERO_CLIENT_ID: Xero OAuth client ID
 * - XERO_CLIENT_SECRET: Xero OAuth client secret
 * - XERO_REDIRECT_URI: Xero OAuth redirect URI
 * - XERO_BASE_URL: Xero API base URL (optional, defaults to production)
 */
import { APIGatewayProxyEvent, Context, APIGatewayProxyResult, SQSEvent } from 'aws-lambda';
import { PrismaClient, Company } from '@prisma/client';
import axiosInstance from '../utils/axiosInstance';
import { refreshXeroToken } from '../services/refreshTokenService';
import { getXeroConfig } from '../config/environment';
import {
    XeroRequestData,
    ValidationError,
    XeroError,
} from '../types';
import dotenv from 'dotenv';
dotenv.config();
import {
    startPaymentsExecution,
    logPaymentsSuccess,
    logPaymentsFailure,
    PaymentsExecutionResult,
} from '../utils/paymentsLogger';
import {
    getCurrentSyncDate,
    formatDateForLogging,
    parseXeroDate,
    generateXeroDateTime
} from '../utils/dateUtils';

// Prisma
let prisma: PrismaClient | null = null;
function getPrismaClient(): PrismaClient {
    if (!prisma) {
        const config = {
            log: ['error', 'warn'] as Array<'error' | 'warn'>,
            errorFormat: 'pretty' as const,
        };
        if (process.env['IS_OFFLINE'] === 'true') {
            delete process.env['PRISMA_QUERY_ENGINE_LIBRARY'];
        }
        prisma = new PrismaClient(config);
    }
    return prisma;
}

// Lambda handler
export const handler = async (
    event: APIGatewayProxyEvent | SQSEvent,
    context: Context
): Promise<APIGatewayProxyResult | void> => {
    if ("Records" in event && event.Records?.length) {
        // SQS
        for (const record of event.Records) {
            try {
                const requestData = JSON.parse(record.body);
                await processRequestWithLogging(requestData, context, 'SYSTEM');
            } catch (err) {
                console.error("Failed to process SQS message", err);
                throw err; // let Lambda/SQS retry
            }
        }
        return;
    } else {
        // API Gateway
        try {
            const requestData = parseRequestData(event);
            await processRequestWithLogging(requestData, context, 'USER');
            return {
                statusCode: 200,
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({
                    success: true,
                    message: "Payment data processed successfully",
                    timestamp: new Date().toISOString(),
                }),
            };
        } catch (err: any) {
            console.error("Handler error:", err);
            return {
                statusCode: err instanceof ValidationError ? 400 : 500,
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({
                    success: false,
                    error: err.message,
                    type: err.constructor.name,
                }),
            };
        }
    }
};

// Wrapper for processRequest with logging
async function processRequestWithLogging(
    requestData: XeroRequestData,
    context: Context,
    triggeredBy: 'USER' | 'SYSTEM' = 'SYSTEM'
): Promise<void> {
    const startTime = Date.now();
    let integrationLogId: string | null = null;
    let processedPayments = 0;
    let insertedPayments = 0;
    let updatedPayments = 0;
    let totalPayments = 0;
    try {
        // Start execution logging
        const { logId } = await startPaymentsExecution(requestData.companyId, triggeredBy);
        integrationLogId = logId;
        // Validate and process
        validateRequestData(requestData);
        const prisma = getPrismaClient();
        const integration = await getActiveIntegration(requestData, prisma);
        if (!integration) {
            throw new Error("Active integration not found");
        }
        const validIntegration = await ensureValidToken(integration);
        // Get last sync date for incremental sync
        const lastSync = await prisma.xeroModuleSync.findFirst({
            where: {
                CompanyId: requestData.companyId,
                ModuleName: 'Payments',
            },
            orderBy: { LastSyncTime: 'desc' },
        });
        let lastSyncDate = lastSync?.LastSyncTime; 
        // Add UpdatedDateUTC filter if not present in where
        let where = requestData.where || '';
        if (lastSyncDate && !where.includes('UpdatedDateUTC')) {
            const updatedDateFilter = `UpdatedDateUTC>=${generateXeroDateTime(lastSyncDate)}`;
            where = where ? `${where}&&${updatedDateFilter}` : updatedDateFilter;
        }
        // Update requestData with the new where
        requestData = { ...requestData, where };
        console.log(requestData);
        // Fetch payments from Xero
        const paymentsData = await getPayments(
            validIntegration.XeroAccessToken!,
            validIntegration.XeroTenantId!,
            requestData
        );
        totalPayments = paymentsData.length;
        if (paymentsData && paymentsData.length > 0) {
            if (requestData.dumpToDatabase) {
                // Save to DB and count inserted/updated
                const { inserted, updated } = await savePaymentsToDatabaseWithCounts(paymentsData, requestData.companyId, prisma);
                processedPayments = paymentsData.length;
                insertedPayments = inserted;
                updatedPayments = updated;
                console.log(`✅ Successfully processed ${processedPayments} payments (${insertedPayments} inserted, ${updatedPayments} updated)`);
            } else {
                processedPayments = paymentsData.length;
                console.log("Data fetched but not dumped to DB (dumpToDatabase is false)");
            }
        } else {
            console.log("No payments found to process");
        }
        // Log success
        if (integrationLogId) {
            const executionResult: PaymentsExecutionResult = {
                totalPayments: totalPayments,
                processedPayments: processedPayments,
                insertedPayments: insertedPayments,
                updatedPayments: updatedPayments,
                errors: 0,
                warnings: 0,
                duration: `${Date.now() - startTime}ms`,
            };
            await logPaymentsSuccess(integrationLogId, startTime, executionResult);
            // Update last sync date for incremental sync
            await updateLastSyncDate(requestData.companyId, getCurrentSyncDate(), prisma);
        }
        const totalDuration = Date.now() - startTime;
        console.log(`✅ Payments sync completed successfully in ${(totalDuration / 1000).toFixed(1)}s`);
    } catch (error: any) {
        // Log failure
        if (integrationLogId) {
            await logPaymentsFailure(integrationLogId, startTime, error, {
                totalPayments,
                processedPayments: processedPayments,
                insertedPayments: insertedPayments,
                updatedPayments: updatedPayments,
                errors: 1,
                warnings: 0,
            });
        }
        const totalDuration = Date.now() - startTime;
        console.error(`❌ Payments sync failed after ${(totalDuration / 1000).toFixed(1)}s:`, {
            error: error.message,
            companyId: requestData.companyId,
        });
        throw error;
    }
}

// parse event
export function parseRequestData(
    event: APIGatewayProxyEvent | SQSEvent | { body?: string }
): XeroRequestData {
    // handle SQS
    if ("Records" in event && event.Records?.[0]) {
        try {
            const parsed = JSON.parse(event.Records[0].body);
            return parsed as XeroRequestData;
        } catch {
            throw new ValidationError("Failed to parse SQS message body");
        }
    }

    // handle API Gateway
    if ("body" in event && event.body) {
        try {
            const parsed =
                typeof event.body === "string" ? JSON.parse(event.body) : event.body;
            return parsed as XeroRequestData;
        } catch {
            throw new ValidationError("Failed to parse API Gateway body");
        }
    }

    throw new ValidationError("Invalid request data");
}

// Validate request data
function validateRequestData(data: XeroRequestData): void {
    if (!data.companyId) {
        throw new ValidationError("companyId is required");
    }
}

// Get active integration
async function getActiveIntegration(data: XeroRequestData, prisma: PrismaClient): Promise<Company> {
    const integration = await prisma.company.findFirst({
        where: {
            Id: data.companyId,
            ConnectionStatus: "ACTIVE",
        },
    });

    if (!integration) {
        throw new Error("Active integration not found for company");
    }

    return integration;
}

// Ensure valid token
async function ensureValidToken(integration: Company): Promise<Company> {
    if (!integration.XeroAccessToken || !integration.XeroTokenExpiry) {
        throw new Error("No access token found");
    }

    const now = new Date();
    const expiry = new Date(integration.XeroTokenExpiry);
    // Add a 10 minute buffer (600,000 ms)
    if (expiry.getTime() - now.getTime() <= 10 * 60 * 1000) {
        console.log("Token expired or about to expire, refreshing...");
        return await refreshXeroToken(integration);
    }

    return integration;
}

// Get payments from Xero
const getPayments = async (
  accessToken: string,
  tenantId: string,
  requestData: XeroRequestData
): Promise<any[]> => {
  try {
    const config = getXeroConfig();
    const url = `${config.baseUrl}/Payments`;
    // Build params with support for where, page, pageSize
    const params: any = {};
    if (requestData.where) params.where = requestData.where;
    if (requestData.page) params.page = requestData.page;
    if (requestData.pageSize) params.pageSize = requestData.pageSize;
    const response = await axiosInstance.get(url, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        'Xero-tenant-id': tenantId,
        Accept: 'application/json',
      },
      params,
    });
    console.log('Xero Payments API raw response:', JSON.stringify(response.data, null, 2));
    if (response.data && response.data.Payments) {
      console.log(`Retrieved ${response.data.Payments.length} payments from Xero`);
      return response.data.Payments;
    }
    return [];
  } catch (error: any) {
    console.error("Error fetching payments from Xero:", error.response?.data || error.message);
    throw new XeroError(`Failed to fetch payments: ${error.response?.data?.Elements?.[0]?.ValidationErrors?.[0]?.Message || error.message}`);
  }
};

// Map Xero Payment to Prisma Payment
function mapXeroPaymentToPrisma(payment: any, companyId: string) {
    return {
        PaymentID: payment.PaymentID,
        PaymentType: payment.PaymentType,
        Date: parseXeroDate(payment.Date),
        AccountID: payment.Account?.AccountID ?? null,
        BankAccountNumber: null, // Not present in Xero response
        AccountCode: payment.Account?.Code ?? null,
        AccountName: null, // Not present in Xero response
        BankAmount: payment.BankAmount ?? null,
        Amount: payment.Amount ?? null,
        CurrencyRate: payment.CurrencyRate ?? null,
        InvoiceID: payment.Invoice?.InvoiceID ?? null,
        InvoiceNumber: payment.Invoice?.InvoiceNumber ?? null,
        InvoiceTotal: null, // Not present in Xero response
        Reference: payment.Reference ?? null,
        Status: payment.Status ?? null,
        UpdateUTCDate: parseXeroDate(payment.UpdatedDateUTC),
        CompanyId: companyId,
    };
}

// Save payments to database and return inserted/updated counts
async function savePaymentsToDatabaseWithCounts(
    payments: any[],
    companyId: string,
    prisma: PrismaClient
): Promise<{ inserted: number; updated: number }> {
    // 1. Map all Xero payments to Prisma Payment records
    const paymentRecords = payments.map(payment => mapXeroPaymentToPrisma(payment, companyId));
    // 2. Fetch all existing Payments for this company
    const existingPayments = await prisma.payment.findMany({ where: { CompanyId: companyId } });
    const existingPaymentIds = new Set(existingPayments.map(p => p.PaymentID));
    // 3. Split into new and existing payments
    const paymentsToInsert = paymentRecords.filter(p => !existingPaymentIds.has(p.PaymentID));
    const paymentsToUpdate = paymentRecords.filter(p => existingPaymentIds.has(p.PaymentID));
    // 4. Bulk insert new payments
    if (paymentsToInsert.length > 0) {
        await prisma.payment.createMany({ data: paymentsToInsert, skipDuplicates: true });
    }
    // 5. Bulk update existing payments (use transaction)
    if (paymentsToUpdate.length > 0) {
        await prisma.$transaction(
            paymentsToUpdate.map(p =>
                prisma.payment.updateMany({ where: { PaymentID: p.PaymentID, CompanyId: companyId }, data: p })
            )
        );
    }
    // Return inserted/updated counts
    return { inserted: paymentsToInsert.length, updated: paymentsToUpdate.length };
}

// Update last sync date in xeroModuleSync table
async function updateLastSyncDate(companyId: string, syncDate: Date, prisma: PrismaClient): Promise<void> {
    try {
        await prisma.xeroModuleSync.upsert({
            where: {
                CompanyId_ModuleName: {
                    CompanyId: companyId,
                    ModuleName: 'Payments'
                }
            },
            update: {
                LastSyncTime: syncDate,
                UpdatedAt: getCurrentSyncDate()
            },
            create: {
                Id: require('uuid').v4(),
                CompanyId: companyId,
                ModuleName: 'Payments',
                LastSyncTime: syncDate,
                CreatedAt: getCurrentSyncDate(),
                UpdatedAt: getCurrentSyncDate()
            }
        });
        console.log(`📅 Updated last sync date to: ${formatDateForLogging(syncDate)}`);
    } catch (error) {
        console.error("Error updating last sync date:", error);
        // Don't throw here as this shouldn't fail the main sync process
    }
}

