generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "debian-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  Id             String           @id @default(uuid()) @db.Uuid
  Name           String?
  Email          String           @unique
  Password       String
  IsActive       Boolean          @default(true)
  IsVerified     Boolean          @default(false)
  RefreshToken   String?
  TokenExpiry    DateTime?
  CreatedAt      DateTime         @default(now())
  UpdatedAt      DateTime         @updatedAt
  LastLoginAt    DateTime?
  Companies      Company[]
  IntegrationLog IntegrationLog[]

  @@map("User")
}

model Company {
  Id                     String                 @id @default(uuid()) @db.Uuid
  UserId                 String                 @db.Uuid
  Name                   String                 @db.VarChar(150)
  XeroTenantId           String?                @db.VarChar(100)
  XeroAccessToken        String?
  XeroRefreshToken       String?
  XeroTokenExpiry        DateTime?
  XeroRefreshTokenExpiry DateTime?
  CreatedAt              DateTime               @default(now())
  UpdatedAt              DateTime               @updatedAt
  FinancialYearEnd       String?                @db.VarChar(20)
  ConnectionStatus       ConnectionStatus       @default(PENDING)
  LastSyncDate           DateTime?
  NextSyncDate           DateTime?
  Accounts               Account[]
  BalanceSheet           BalanceSheet[]
  BalanceSheetTracking   BalanceSheetTracking[]
  BankTransactions       BankTransaction[]
  BankTransactionLine    BankTransactionLine[]
  BankTransfer           BankTransfer[]
  Budget                 Budget[]
  User                   User                   @relation(fields: [UserId], references: [Id])
  Contact                Contact[]
  CreditNote             CreditNote[]
  CreditNoteLine         CreditNoteLine[]
  Currency               Currency[]
  Employee               Employee[]
  ExpenseClaim           ExpenseClaim[]
  IntegrationLog         IntegrationLog[]
  Invoice                Invoice[]
  InvoiceLine            InvoiceLine[]
  Item                   Item[]
  Journal                Journal[]
  JournalLine            JournalLine[]
  ManualJournal          ManualJournal[]
  ManualJournalLine      ManualJournalLine[]
  Organisation           Organisation[]
  Payment                Payment[]
  ProfitLoss             ProfitLoss[]
  ProfitLossTracking     ProfitLossTracking[]
  Receipt                Receipt[]
  TaxRate                TaxRate[]
  TaxRateLine            TaxRateLine[]
  TrackingCategory       TrackingCategory[]
  TrackingCategoryLine   TrackingCategoryLine[]
  TrialBalance           TrialBalance[]
  XeroModuleSync         XeroModuleSync[]

  @@map("Company")
}

model Account {
  BankAccountType         String?   @db.VarChar(50)
  BankAccountNumber       String?   @db.VarChar(50)
  Code                    String?   @db.VarChar(10)
  AccountClassTypes       String?   @db.VarChar(50)
  Type                    String?   @db.VarChar(50)
  Name                    String?   @db.VarChar(150)
  Description             String?
  ReportingCode           String?   @db.VarChar(200)
  ReportingCodeName       String?   @db.VarChar(500)
  CurrencyCode            String?   @db.VarChar(50)
  TaxType                 String?   @db.VarChar(50)
  SystemAccount           String?   @db.VarChar(50)
  Status                  String?   @db.VarChar(50)
  EnablePaymentsToAccount Boolean?
  ShowInExpenseClaims     Boolean?
  UpdateUtcDate           DateTime? @db.Date
  CompanyId               String    @db.Uuid
  AccountID               String    @id @db.Uuid
  Company                 Company   @relation(fields: [CompanyId], references: [Id])

  @@map("Account")
}

model BankTransaction {
  BankTransactionID   String                @id @default(uuid()) @db.Uuid
  Type                String?               @db.VarChar(20)
  AccountID           String                @db.Uuid
  BankAccountNumber   String?               @db.VarChar(50)
  Code                String?               @db.VarChar(10)
  BankAccountName     String?               @db.VarChar(150)
  ContactID           String?               @db.Uuid
  ContactName         String?               @db.VarChar(255)
  ContactFirstName    String?               @db.VarChar(255)
  ContactLastName     String?               @db.VarChar(255)
  CurrencyCode        String?               @db.VarChar(3)
  Date                DateTime?             @db.Date
  DueDate             DateTime?             @db.Date
  FullyPaidOnDate     DateTime?             @db.Date
  IsReconciled        Boolean?
  LineAmountTypes     String?               @db.VarChar(10)
  Reference           String?               @db.VarChar(255)
  Status              String?               @db.VarChar(10)
  SubTotal            Decimal?              @db.Decimal(18, 2)
  TotalTax            Decimal?              @db.Decimal(18, 2)
  Total               Decimal?              @db.Decimal(18, 2)
  TotalMovement       Decimal?              @db.Decimal(18, 2)
  UpdateUTCDate       DateTime?             @db.Date
  CompanyId           String                @db.Uuid
  CurrencyRate        Decimal?              @db.Decimal(18, 4)
  Company             Company               @relation(fields: [CompanyId], references: [Id])
  BankTransactionLine BankTransactionLine[]

  @@map("BankTransaction")
}

model BankTransactionLine {
  Id                     String          @id @default(uuid()) @db.Uuid
  BankTransactionId      String          @db.Uuid
  LineItemId             String          @db.Uuid
  ContactId              String?         @db.Uuid
  ContactName            String?         @db.VarChar(255)
  Date                   DateTime?       @db.Date
  Reference              String?         @db.VarChar(255)
  Description            String?
  Quantity               Decimal?        @db.Decimal(18, 2)
  UnitAmount             Decimal?        @db.Decimal(18, 2)
  AccountCode            String?         @db.VarChar(10)
  TaxAmount              Decimal?        @db.Decimal(18, 2)
  LineAmount             Decimal?        @db.Decimal(18, 2)
  TaxType                String?         @db.VarChar(30)
  TrackingCategory1      String?
  TrackingCategory1Value String?
  TrackingCategory2      String?
  TrackingCategory2Value String?
  CurrencyCode           String?         @db.VarChar(20)
  CurrencyRate           Decimal?        @db.Decimal(18, 4)
  CompanyId              String          @db.Uuid
  BankTransaction        BankTransaction @relation(fields: [BankTransactionId], references: [BankTransactionID])
  Company                Company         @relation(fields: [CompanyId], references: [Id])

  @@map("BankTransactionLine")
}

model BankTransfer {
  BankTransferID           String    @id @default(uuid()) @db.Uuid
  FromBankAccountAccountID String?   @db.Uuid
  FromBankAccountName      String?   @db.VarChar(255)
  ToBankAccountAccountID   String?   @db.Uuid
  ToBankAccountName        String?   @db.VarChar(255)
  Amount                   Decimal?  @db.Decimal(18, 2)
  FromBankTransactionID    String?   @db.Uuid
  ToBankTransactionID      String?   @db.Uuid
  FromIsReconciled         Boolean?
  ToIsReconciled           Boolean?
  CurrencyRate             Decimal?  @db.Decimal(18, 4)
  Reference                String?   @db.VarChar(50)
  CreatedDateUTC           DateTime? @db.Date
  UpdateUTCDate            DateTime? @db.Date
  CompanyId                String    @db.Uuid
  Company                  Company   @relation(fields: [CompanyId], references: [Id])

  @@map("BankTransfer")
}

model Budget {
  BudgetID      String    @id @default(uuid()) @db.Uuid
  BudgetType    String?   @db.VarChar(50)
  Description   String?   @db.VarChar(255)
  Trackings     Int?
  UpdateUTCDate DateTime? @db.Date
  CompanyId     String    @db.Uuid
  Company       Company   @relation(fields: [CompanyId], references: [Id])

  @@map("Budget")
}

model Contact {
  ContactID                      String    @id @default(uuid()) @db.Uuid
  ContactNumber                  String?   @db.VarChar(50)
  ContactStatus                  String?   @db.VarChar(10)
  AccountNumber                  String?   @db.VarChar(50)
  Name                           String?   @db.VarChar(255)
  FirstName                      String?   @db.VarChar(255)
  LastName                       String?   @db.VarChar(255)
  EmailAddress                   String?   @db.VarChar(255)
  SkypeUserName                  String?   @db.VarChar(255)
  DefaultCurrency                String?   @db.VarChar(50)
  BankAccountDetails             String?   @db.VarChar(50)
  TaxNumber                      String?   @db.VarChar(50)
  AccountsReceivableTaxType      String?   @db.VarChar(50)
  AccountsPayableTaxType         String?   @db.VarChar(50)
  IsSupplier                     Boolean?
  IsCustomer                     Boolean?
  PurchasesDefaultAccountCode    String?   @db.VarChar(50)
  SalesDefaultAccountCode        String?   @db.VarChar(50)
  BatchPaymentsBankAccountNumber String?   @db.VarChar(30)
  BatchPaymentsBankAccountName   String?   @db.VarChar(30)
  BatchPaymentsDetails           String?
  AccountsReceivableOutstanding  Decimal?  @db.Decimal(18, 2)
  AccountsReceivableOverdue      Decimal?  @db.Decimal(18, 2)
  AccountsPayableOutstanding     Decimal?  @db.Decimal(18, 2)
  AccountsPayableOverdue1        Decimal?  @db.Decimal(18, 2)
  PhoneDefaultNumber             String?   @db.VarChar(50)
  PhoneDefaultAreaCode           String?   @db.VarChar(10)
  PhoneDefaultCountryCode        String?   @db.VarChar(20)
  PhoneDDINumber                 String?   @db.VarChar(50)
  PhoneDDIAreaCode               String?   @db.VarChar(10)
  PhoneDDICountryCode            String?   @db.VarChar(20)
  PhoneMobileNumber              String?   @db.VarChar(50)
  PhoneMobileAreaCode            String?   @db.VarChar(10)
  PhoneMobileCountryCode         String?   @db.VarChar(20)
  PhoneFaxNumber                 String?   @db.VarChar(50)
  PhoneFaxAreaCode               String?   @db.VarChar(10)
  PhoneFaxCountryCode            String?   @db.VarChar(20)
  MailingAddressAttensionTo      String?   @db.VarChar(255)
  MailingAddressAddressLine1     String?   @db.VarChar(500)
  MailingAddressAddressLine2     String?   @db.VarChar(500)
  MailingAddressAddressLine3     String?   @db.VarChar(500)
  MailingAddressAddressLine4     String?   @db.VarChar(500)
  MailingAddressCity             String?   @db.VarChar(255)
  MailingAddressCountry          String?   @db.VarChar(50)
  MailingAddressPostalCode       String?   @db.VarChar(50)
  MailingAddressRegion           String?   @db.VarChar(255)
  StreetAttensionTo              String?   @db.VarChar(255)
  StreetAddressLine1             String?   @db.VarChar(500)
  StreetAddressLine2             String?   @db.VarChar(500)
  StreetAddressLine3             String?   @db.VarChar(500)
  StreetAddressLine4             String?   @db.VarChar(500)
  StreetCity                     String?   @db.VarChar(255)
  StreetCountry                  String?   @db.VarChar(50)
  StreetPostalCode               String?   @db.VarChar(50)
  StreetRegion                   String?   @db.VarChar(255)
  ContactGroups                  String?   @db.VarChar(500)
  UpdateUTCDate                  DateTime? @db.Date
  CompanyId                      String    @db.Uuid
  Company                        Company   @relation(fields: [CompanyId], references: [Id])

  @@map("Contact")
}

model CreditNote {
  CreditNoteID          String           @id @default(uuid()) @db.Uuid
  Type                  String?          @db.VarChar(15)
  CreditNoteNumber      String?          @db.VarChar(20)
  ContactId             String?          @db.Uuid
  ContactName           String?          @db.VarChar(255)
  ContactFirstName      String?          @db.VarChar(255)
  ContactLastName       String?          @db.VarChar(255)
  Date                  DateTime?        @db.Date
  DueDate               DateTime?        @db.Date
  FullyPaidOnDate       DateTime?        @db.Date
  LineAmountType        String?          @db.VarChar(20)
  Reference             String?          @db.VarChar(50)
  Status                String?          @db.VarChar(10)
  AmountDue             Decimal?         @db.Decimal(18, 2)
  AmountPaid            Decimal?         @db.Decimal(18, 2)
  AmountAllocated       Decimal?         @db.Decimal(18, 2)
  AmountDueToDate       Decimal?         @db.Decimal(18, 2)
  AmountPaidToDate      Decimal?         @db.Decimal(18, 2)
  AmountAllocatedToDate Decimal?         @db.Decimal(18, 2)
  RemainingCredit       Decimal?         @db.Decimal(18, 2)
  CurrencyCode          String?          @db.VarChar(3)
  CurrencyRate          Decimal?         @db.Decimal(18, 4)
  SubTotal              Decimal?         @db.Decimal(18, 2)
  TotalTax              Decimal?         @db.Decimal(18, 2)
  Total                 Decimal?         @db.Decimal(18, 2)
  UpdateUTCDate         DateTime?        @db.Date
  CompanyId             String           @db.Uuid
  Company               Company          @relation(fields: [CompanyId], references: [Id])
  CreditNoteLine        CreditNoteLine[]

  @@map("CreditNote")
}

model CreditNoteLine {
  Id                     String     @id @default(uuid()) @db.Uuid
  CreditNoteId           String     @db.Uuid
  LineItemId             String?    @db.Uuid
  CreditNoteNumber       String?    @db.VarChar(10)
  ContactId              String?    @db.Uuid
  ContactName            String?    @db.VarChar(255)
  Date                   DateTime?  @db.Date
  Reference              String?    @db.VarChar(50)
  ItemCode               String?    @db.VarChar(30)
  Description            String?
  Quantity               Decimal?   @db.Decimal(18, 2)
  UnitAmount             Decimal?   @db.Decimal(18, 2)
  UnitCost               Decimal?   @db.Decimal(18, 2)
  AccountCode            String?    @db.VarChar(10)
  TaxAmount              Decimal?   @db.Decimal(18, 2)
  TaxType                String?    @db.VarChar(30)
  LineAmount             Decimal?   @db.Decimal(18, 2)
  TrackingCategory1      String?
  TrackingCategory1Value String?
  TrackingCategory2      String?
  TrackingCategory2Value String?
  CurrencyCode           String?    @db.VarChar(20)
  CurrencyRate           Decimal?   @db.Decimal(18, 4)
  CompanyId              String     @db.Uuid
  Company                Company    @relation(fields: [CompanyId], references: [Id])
  CreditNote             CreditNote @relation(fields: [CreditNoteId], references: [CreditNoteID])

  @@map("CreditNoteLine")
}

model Currency {
  Code          String    @id @db.VarChar(3)
  Description   String?   @db.VarChar(100)
  UpdateUTCDate DateTime? @db.Date
  CompanyId     String    @db.Uuid
  Company       Company   @relation(fields: [CompanyId], references: [Id])

  @@map("Currency")
}

model Employee {
  EmployeeID              String    @id @default(uuid()) @db.Uuid
  Status                  String?   @db.VarChar(50)
  FirstName               String?   @db.VarChar(255)
  LastName                String?   @db.VarChar(255)
  ExternalLinkUrl         String?   @db.VarChar(255)
  ExternalLinkDescription String?
  UpdateUTCDate           DateTime? @db.Date
  CompanyId               String    @db.Uuid
  Company                 Company   @relation(fields: [CompanyId], references: [Id])

  @@map("Employee")
}

model ExpenseClaim {
  ExpenseClaimID     String    @id @db.VarChar(50)
  Status             String?   @db.VarChar(50)
  UserID             String?   @db.Uuid
  EmailAddress       String?
  FirstName          String?
  LastName           String?
  UserUpdatedDateUTC DateTime? @db.Timestamp(6)
  IsSubscriber       Boolean?
  OrganisationRole   String?   @db.VarChar(50)
  Total              Decimal?  @db.Decimal(18, 2)
  AmountDue          Decimal?  @db.Decimal(18, 2)
  AmountPaid         Decimal?  @db.Decimal(18, 2)
  PaymentDueDate     DateTime? @db.Timestamp(6)
  ReportingDate      DateTime? @db.Timestamp(6)
  UpdateUTCDate      DateTime? @db.Timestamp(6)
  CompanyId          String    @db.Uuid
  Company            Company   @relation(fields: [CompanyId], references: [Id])

  @@map("ExpenseClaim")
}

model Invoice {
  InvoiceID            String        @id @default(uuid()) @db.Uuid
  Type                 String?       @db.VarChar(20)
  InvoiceNumber        String?
  ContactID            String?       @db.Uuid
  ContactName          String?       @db.VarChar(255)
  FirstName            String?       @db.VarChar(255)
  LastName             String?       @db.VarChar(255)
  Date                 DateTime?     @db.Date
  DueDate              DateTime?     @db.Date
  FullyPaidOnDate      DateTime?     @db.Date
  ExpectedPaymentDate  DateTime?     @db.Date
  PlannedPaymentDate   DateTime?     @db.Date
  LineAmountTypes      String?       @db.VarChar(9)
  Reference            String?       @db.VarChar(255)
  Status               String?       @db.VarChar(10)
  HasAttachments       Boolean?
  SentToContact        Boolean?
  BrandingThemeID      String?       @db.Uuid
  BrandingThemeName    String?       @db.VarChar(30)
  AmountDue            Decimal?      @db.Decimal(18, 2)
  AmountPaid           Decimal?      @db.Decimal(18, 2)
  AmountCredited       Decimal?      @db.Decimal(18, 2)
  AmountPaidToDate     Decimal?      @db.Decimal(18, 2)
  AmountDueToDate      Decimal?      @db.Decimal(18, 2)
  AmountCreditedToDate Decimal?      @db.Decimal(18, 2)
  CurrencyCode         String?       @db.VarChar(3)
  CurrencyRate         Decimal?      @db.Decimal(18, 4)
  SubTotal             Decimal?      @db.Decimal(18, 2)
  TotalDiscount        Decimal?      @db.Decimal(18, 2)
  TotalTax             Decimal?      @db.Decimal(18, 2)
  Total                Decimal?      @db.Decimal(18, 2)
  UpdateUTCDate        DateTime?     @db.Date
  CompanyId            String        @db.Uuid
  Company              Company       @relation(fields: [CompanyId], references: [Id])
  InvoiceLine          InvoiceLine[]
  Payment              Payment[]

  @@map("Invoice")
}

model InvoiceLine {
  Id                     String    @id @default(uuid()) @db.Uuid
  InvoiceID              String    @db.Uuid
  InvoiceNumber          String?
  ContactID              String?   @db.Uuid
  ContactName            String?   @db.VarChar(255)
  Date                   DateTime? @db.Date
  Reference              String?   @db.VarChar(255)
  LineItemID             String    @db.Uuid
  ItemCode               String?   @db.VarChar(30)
  Description            String?
  Quantity               Decimal?  @db.Decimal(18, 2)
  UnitAmount             Decimal?  @db.Decimal(18, 2)
  UnitCost               Decimal?  @db.Decimal(18, 2)
  DiscountRate           Decimal?  @db.Decimal(18, 2)
  AccountCode            String?   @db.VarChar(10)
  AccountID              String?   @db.Uuid
  TaxAmount              Decimal?  @db.Decimal(18, 2)
  TaxType                String?   @db.VarChar(30)
  LineAmountTypes        String?   @db.VarChar(10)
  LineAmount             Decimal?  @db.Decimal(18, 2)
  LineAmountIncl         Decimal?  @db.Decimal(18, 2)
  LineAmountExcl         Decimal?  @db.Decimal(18, 2)
  CompanyId              String    @db.Uuid
  TrackingCategory1      String?   @db.VarChar(100)
  TrackingCategory1Value String?   @db.VarChar(100)
  TrackingCategory2      String?   @db.VarChar(100)
  TrackingCategory2Value String?   @db.VarChar(100)
  CurrencyCode           String?   @db.VarChar(20)
  CurrencyRate           Decimal?  @db.Decimal(18, 4)
  Company                Company   @relation(fields: [CompanyId], references: [Id])
  Invoice                Invoice   @relation(fields: [InvoiceID], references: [InvoiceID])

  @@map("InvoiceLine")
}

model Item {
  ItemID                    String    @id @default(uuid()) @db.Uuid
  Name                      String?   @db.VarChar(50)
  Code                      String?   @db.VarChar(30)
  Description               String?
  PurchaseDescription       String?
  IsPurchased               Boolean?
  IsSold                    Boolean?
  IsTrackedAsInventory      Boolean?
  TotalCostPool             Decimal?  @db.Decimal(18, 2)
  QuantityOnHand            Decimal?  @db.Decimal(18, 2)
  InventoryAssetAccountCode String?   @db.VarChar(50)
  PurchaseAccountCode       String?   @db.VarChar(10)
  PurchaseTaxType           String?   @db.VarChar(50)
  PurchaseUnitPrice         Decimal?  @db.Decimal(18, 2)
  COGSAccountCode           String?   @db.VarChar(10)
  SalesTaxType              String?   @db.VarChar(50)
  SalesAccountCode          String?   @db.VarChar(10)
  SalesUnitPrice            Decimal?  @db.Decimal(18, 2)
  UpdateUTCDate             DateTime? @db.Date
  CompanyId                 String    @db.Uuid
  Company                   Company   @relation(fields: [CompanyId], references: [Id])

  @@map("Item")
}

model Journal {
  JournalID        String        @id @default(uuid()) @db.Uuid
  JournalNumber    Int?
  CreatedDateUTC   DateTime?     @db.Date
  JournalDate      DateTime?     @db.Date
  Reference        String?       @db.VarChar(255)
  SourceType       String?       @db.VarChar(30)
  SourceID         String?       @db.VarChar(150)
  TotalGrossAmount Decimal?      @db.Decimal(18, 2)
  TotalNetAmount   Decimal?      @db.Decimal(18, 2)
  TotalTaxAmount   Decimal?      @db.Decimal(18, 2)
  UpdateUTCDate    DateTime?     @db.Date
  CompanyId        String        @db.Uuid
  Company          Company       @relation(fields: [CompanyId], references: [Id])
  JournalLine      JournalLine[]

  @@map("Journal")
}

model JournalLine {
  Id                     String    @id @default(uuid()) @db.Uuid
  JournalID              String    @db.Uuid
  JournalNumber          Int?
  JournalDate            DateTime? @db.Date
  Reference              String?   @db.VarChar(255)
  JournalLineID          String    @db.Uuid
  SourceType             String?   @db.VarChar(30)
  SourceID               String?   @db.VarChar(150)
  AccountID              String?   @db.Uuid
  AccountCode            String?   @db.VarChar(10)
  AccountType            String?   @db.VarChar(50)
  AccountName            String?   @db.VarChar(50)
  Description            String?
  NetAmount              Decimal?  @db.Decimal(18, 2)
  GrossAmount            Decimal?  @db.Decimal(18, 2)
  Debit                  Decimal?  @db.Decimal(18, 2)
  Credit                 Decimal?  @db.Decimal(18, 2)
  TaxAmount              Decimal?  @db.Decimal(18, 2)
  TaxType                String?   @db.VarChar(30)
  TaxName                String?   @db.VarChar(60)
  CompanyId              String    @db.Uuid
  TrackingCategory1      String?   @db.VarChar(100)
  TrackingCategory1Value String?   @db.VarChar(100)
  TrackingCategory2      String?   @db.VarChar(100)
  TrackingCategory2Value String?   @db.VarChar(100)
  Status                 String    @db.VarChar(20)
  Company                Company   @relation(fields: [CompanyId], references: [Id])
  Journal                Journal   @relation(fields: [JournalID], references: [JournalID])

  @@map("JournalLine")
}

model ManualJournal {
  ManualJournalID   String              @id @default(uuid()) @db.Uuid
  Date              DateTime?           @db.Date
  Status            String?             @db.VarChar(50)
  LineAmountTypes   String?             @db.VarChar(50)
  Narration         String?             @db.VarChar(255)
  UpdateUTCDate     DateTime?           @db.Date
  CompanyId         String              @db.Uuid
  Company           Company             @relation(fields: [CompanyId], references: [Id])
  ManualJournalLine ManualJournalLine[]

  @@map("ManualJournal")
}

model ManualJournalLine {
  Id                     String        @id @default(uuid()) @db.Uuid
  ManualJournalID        String        @db.Uuid
  Description            String?
  TaxType                String?       @db.VarChar(50)
  TaxAmount              Decimal?      @db.Decimal(18, 2)
  Debit                  Decimal?      @db.Decimal(18, 2)
  Credit                 Decimal?      @db.Decimal(18, 2)
  AccountCode            String?       @db.VarChar(50)
  AccountID              String        @db.Uuid
  Region                 String?       @db.VarChar(100)
  TrackingCategory1      String?       @db.VarChar(100)
  TrackingCategory1Value String?       @db.VarChar(100)
  TrackingCategory2      String?       @db.VarChar(100)
  TrackingCategory2Value String?       @db.VarChar(100)
  IsBlank                Boolean?
  IsRecentUpdatedInTable Boolean
  CompanyId              String        @db.Uuid
  Company                Company       @relation(fields: [CompanyId], references: [Id])
  ManualJournal          ManualJournal @relation(fields: [ManualJournalID], references: [ManualJournalID])

  @@map("ManualJournalLine")
}

model Organisation {
  OrganisationID             String    @id @default(uuid()) @db.Uuid
  Name                       String?   @db.VarChar(150)
  LegalName                  String?   @db.VarChar(150)
  ShortCode                  String?   @db.VarChar(50)
  OrganisationType           String?   @db.VarChar(50)
  CountryCode                String?   @db.VarChar(50)
  BaseCurrency               String?   @db.VarChar(50)
  IsDemoCompany              Boolean?
  FinancialYearEndDay        Int?
  FinancialYearEndMonth      Int?
  EndOfYearLockDate          DateTime? @db.Date
  PeriodLockDate             DateTime? @db.Date
  PaysTax                    Boolean?
  TaxNumber                  String?   @db.VarChar(50)
  Timezone                   String?   @db.VarChar(50)
  Version                    String?   @db.VarChar(50)
  MailingAddressAttensionTo  String?   @db.VarChar(255)
  MailingAddressAddressLine1 String?   @db.VarChar(500)
  MailingAddressAddressLine2 String?   @db.VarChar(500)
  MailingAddressAddressLine3 String?   @db.VarChar(500)
  MailingAddressAddressLine4 String?   @db.VarChar(500)
  MailingAddressCity         String?   @db.VarChar(255)
  MailingAddressCountry      String?   @db.VarChar(50)
  MailingAddressPostalCode   String?   @db.VarChar(50)
  MailingAddressRegion       String?   @db.VarChar(255)
  StreetAttensionTo          String?   @db.VarChar(255)
  StreetAddressLine1         String?   @db.VarChar(500)
  StreetAddressLine2         String?   @db.VarChar(500)
  StreetAddressLine3         String?   @db.VarChar(500)
  StreetAddressLine4         String?   @db.VarChar(500)
  StreetCity                 String?   @db.VarChar(255)
  StreetCountry              String?   @db.VarChar(50)
  StreetPostalCode           String?   @db.VarChar(50)
  StreetRegion               String?   @db.VarChar(255)
  PhoneDefaultNumber         String?   @db.VarChar(50)
  PhoneDefaultAreaCode       String?   @db.VarChar(50)
  PhoneDefaultCountryCode    String?   @db.VarChar(50)
  PhoneDDINumber             String?   @db.VarChar(50)
  PhoneDDIAreaCode           String?   @db.VarChar(50)
  PhoneDDICountryCode        String?   @db.VarChar(50)
  PhoneMobileNumber          String?   @db.VarChar(50)
  PhoneMobileAreaCode        String?   @db.VarChar(50)
  PhoneMobileCountryCode     String?   @db.VarChar(50)
  PhoneFaxNumber             String?   @db.VarChar(50)
  PhoneFaxAreaCode           String?   @db.VarChar(50)
  PhoneFaxCountryCode        String?   @db.VarChar(50)
  PhoneOfficeNumber          String?   @db.VarChar(50)
  PhoneOfficeAreaCode        String?   @db.VarChar(50)
  PhoneOfficeCountryCode     String?   @db.VarChar(50)
  UpdateUTCDate              DateTime? @db.Date
  CompanyId                  String    @db.Uuid
  APIKey                     String?   @db.VarChar(150)
  Company                    Company   @relation(fields: [CompanyId], references: [Id])

  @@map("Organisation")
}

model Payment {
  PaymentID         String    @id @default(uuid()) @db.Uuid
  PaymentType       String?   @db.VarChar(30)
  Date              DateTime? @db.Timestamp(6)
  AccountID         String    @db.Uuid
  BankAccountNumber String?   @db.VarChar(50)
  AccountCode       String?   @db.VarChar(10)
  AccountName       String?   @db.VarChar(150)
  BankAmount        Decimal?  @db.Decimal(18, 2)
  Amount            Decimal?  @db.Decimal(18, 2)
  CurrencyRate      Decimal?  @db.Decimal(18, 4)
  InvoiceID         String?   @db.Uuid
  InvoiceNumber     String?
  InvoiceTotal      Decimal?  @db.Decimal(18, 2)
  Reference         String?   @db.VarChar(255)
  Status            String?   @db.VarChar(10)
  UpdateUTCDate     DateTime? @db.Date
  CompanyId         String    @db.Uuid
  Company           Company   @relation(fields: [CompanyId], references: [Id])
  Invoice           Invoice?  @relation(fields: [InvoiceID], references: [InvoiceID])

  @@map("Payment")
}

model Receipt {
  ReceiptsID      String    @id @db.VarChar(50)
  ReceiptNumber   String?   @db.VarChar(50)
  Status          String?   @db.VarChar(50)
  UserID          String?   @db.VarChar(50)
  FirstName       String?
  LastName        String?
  ContactID       String?   @db.VarChar(50)
  ContactName     String?
  Date            DateTime? @db.Timestamp(6)
  Reference       String?
  LineAmountTypes String?   @db.VarChar(50)
  SubTotal        Decimal?  @db.Decimal(18, 2)
  TotalTax        Decimal?  @db.Decimal(18, 2)
  Total           Decimal?  @db.Decimal(18, 2)
  HasAttachments  Boolean?
  UpdateUTCDate   DateTime? @db.Timestamp(6)
  CompanyId       String    @db.Uuid
  Company         Company   @relation(fields: [CompanyId], references: [Id])

  @@map("Receipt")
}

model TaxRate {
  Id                    String    @id @default(uuid()) @db.Uuid
  Name                  String?
  TaxType               String?   @db.VarChar(50)
  ReportTaxType         String?   @db.VarChar(50)
  CanApplyToAssets      Boolean?
  CanApplyToEquity      Boolean?
  CanApplyToExpenses    Boolean?
  CanApplyToLiabilities Boolean?
  CanApplyToRevenue     Boolean?
  DisplayTaxRate        Decimal?  @db.Decimal(18, 2)
  EffectiveRate         Decimal?  @db.Decimal(18, 2)
  Status                String?   @db.VarChar(50)
  UpdateUTCDate         DateTime? @db.Date
  CompanyId             String    @db.Uuid
  Company               Company   @relation(fields: [CompanyId], references: [Id])

  @@map("TaxRate")
}

model TaxRateLine {
  Id                     String   @id @default(uuid()) @db.Uuid
  TaxRateName            String?
  TaxComponentsName      String?
  Rate                   Decimal? @db.Decimal(18, 2)
  IsCompound             Boolean?
  IsNonRecoverable       Boolean?
  CompanyId              String   @db.Uuid
  IsRecentUpdatedInTable Boolean
  Company                Company  @relation(fields: [CompanyId], references: [Id])

  @@map("TaxRateLine")
}

model TrackingCategory {
  TrackingCategoryID   String                 @id @default(uuid()) @db.Uuid
  CategoryName         String?                @db.VarChar(100)
  TotalOptions         Int?
  Status               String?                @db.VarChar(50)
  UpdateUTCDate        DateTime?              @db.Date
  CompanyId            String                 @db.Uuid
  Company              Company                @relation(fields: [CompanyId], references: [Id])
  TrackingCategoryLine TrackingCategoryLine[]

  @@map("TrackingCategory")
}

model TrackingCategoryLine {
  Id                     String           @id @default(uuid()) @db.Uuid
  TrackingCategoryID     String           @db.Uuid
  TrackingOptionID       String           @db.Uuid
  Status                 String?          @db.VarChar(50)
  Name                   String?          @db.VarChar(255)
  CompanyId              String           @db.Uuid
  IsRecentUpdatedInTable Boolean
  Company                Company          @relation(fields: [CompanyId], references: [Id])
  TrackingCategory       TrackingCategory @relation(fields: [TrackingCategoryID], references: [TrackingCategoryID])

  @@map("TrackingCategoryLine")
}

model ProfitLossTracking {
  Id                  String  @id @default(uuid()) @db.Uuid
  Year                Int
  Month               Int
  AccountId           String
  AccountName         String
  Amount              Decimal @db.Decimal(18, 2)
  TrackingCategoryId1 String?
  TrackingCategoryId2 String?
  CompanyId           String  @db.Uuid
  Company             Company @relation(fields: [CompanyId], references: [Id])
}

model BalanceSheet {
  Id          String  @id @db.Uuid
  Year        Int
  Month       Int
  AccountId   String
  AccountName String
  Amount      Decimal @db.Decimal(18, 2)
  CompanyId   String  @db.Uuid
  Company     Company @relation(fields: [CompanyId], references: [Id])
}

model BalanceSheetTracking {
  Id                  String  @id @db.Uuid
  Year                Int
  Month               Int
  AccountId           String
  AccountName         String
  Amount              Decimal @db.Decimal(18, 2)
  TrackingCategoryId1 String?
  TrackingCategoryId2 String?
  CompanyId           String  @db.Uuid
  Company             Company @relation(fields: [CompanyId], references: [Id])
}

model IntegrationLog {
  Id                 String     @id @db.Uuid
  RequestId          String     @db.VarChar(100)
  CompanyId          String     @db.Uuid
  ApiName            String     @db.VarChar(100)
  Method             String?    @db.VarChar(10)
  ApiUrl             String?    @db.VarChar(500)
  IntegrationName    String     @default("Xero") @db.VarChar(100)
  StatusCode         String?    @db.VarChar(20)
  Duration           String?    @db.VarChar(20)
  Message            String?
  Entity             String?    @db.VarChar(100)
  TriggeredBy        String?    @db.VarChar(20)
  SyncStatus         SyncStatus @default(PENDING)
  ApiRequest         Json?
  ApiResponse        Json?
  ErrorDetails       Json?
  SyncSummary        Json?
  StartedAt          DateTime   @default(now())
  CompletedAt        DateTime?
  CreatedAt          DateTime   @default(now())
  UpdatedAt          DateTime
  BatchId            String?    @db.VarChar(100)
  EntityCount        Int?
  FailureCount       Int?
  IsParentLog        Boolean    @default(false)
  LastRetryAt        DateTime?
  MaxRetries         Int        @default(3)
  NextRetryAt        DateTime?
  ParentLogId        String?    @db.Uuid
  ProcessedRecords   Int?
  ProgressPercentage Decimal?   @db.Decimal(5, 2)
  RetryCount         Int        @default(0)
  SkippedCount       Int?
  SuccessCount       Int?
  SyncType           SyncType   @default(SINGLE_ENTITY)
  TotalRecords       Int?
  userId             String?    @db.Uuid
  Company            Company    @relation(fields: [CompanyId], references: [Id])
  User               User?      @relation(fields: [userId], references: [Id])

  @@index([CompanyId, SyncStatus])
  @@index([CreatedAt])
  @@index([RequestId])
}

model ProfitLoss {
  Id          String  @id @db.Uuid
  Year        Int
  Month       Int
  AccountId   String
  AccountName String
  Amount      Decimal @db.Decimal(18, 2)
  CompanyId   String  @db.Uuid
  Company     Company @relation(fields: [CompanyId], references: [Id])
}

model TrialBalance {
  Id                   String  @id @db.Uuid
  Year                 Int
  Month                Int
  AccountId            String
  AccountName          String
  Amount               Decimal @db.Decimal(18, 2)
  CompanyId            String  @db.Uuid
  monthEndDebitAmount  Decimal @db.Decimal(15, 2)
  monthEndCreditAmount Decimal @db.Decimal(15, 2)
  netChangeAmount      Decimal @db.Decimal(15, 2)
  Company              Company @relation(fields: [CompanyId], references: [Id])
}

model XeroModuleSync {
  Id           String    @id @db.Uuid
  CompanyId    String    @db.Uuid
  ModuleName   String    @db.VarChar(100)
  LastSyncTime DateTime?
  CreatedAt    DateTime  @default(now())
  UpdatedAt    DateTime
  Company      Company   @relation(fields: [CompanyId], references: [Id])

  @@unique([CompanyId, ModuleName])
}

enum ConnectionStatus {
  ACTIVE
  EXPIRED
  DISCONNECTED
  PENDING
}

enum SyncStatus {
  PENDING
  IN_PROGRESS
  SUCCESS
  WARNING
  ERROR
  RETRYING
  CANCELLED
}

enum SyncType {
  SINGLE_ENTITY
  MULTIPLE_ENTITY
  FULL_SYNC
}
