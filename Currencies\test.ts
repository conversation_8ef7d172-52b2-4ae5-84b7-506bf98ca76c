import { SQSClient, SendMessageCommand } from '@aws-sdk/client-sqs';

const sqs = new SQSClient({
  region: 'us-east-1',
  endpoint: 'http://localhost:9324', // local endpoint
  credentials: {
    accessKeyId: 'root',
    secretAccessKey: 'root',
  },
});

async function sendTestMessage() {
  const command = new SendMessageCommand({
    QueueUrl: 'http://localhost:9324/queue/budgets-sync-queue',
    MessageBody: JSON.stringify({
      userId: 'test-user',
      companyId: 'test-company',
      dumpToDatabase: true,
    }),
  });

  await sqs.send(command);
  console.log('✅ Test message sent!');
}

sendTestMessage().catch(console.error);
