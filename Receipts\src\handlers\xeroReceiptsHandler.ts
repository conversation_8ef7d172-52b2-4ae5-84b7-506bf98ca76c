/**
 * Xero Receipts Data Synchronization Handler
 *
 * This AWS Lambda handler synchronizes Receipt data from Xero API.
 *
 * Key Features:
 * - Bulk insert and update of receipts
 * - Handles both API Gateway and SQS events
 * - Automatic Xero token refresh
 * - Comprehensive error handling and logging
 * - Incremental sync support
 *
 * Usage:
 * - API Gateway: POST /receipts/sync with { companyId: "uuid" }
 * - SQS: Queue message with { companyId: "uuid", tenantId?: "string" }
 *
 * Environment Variables Required:
 * - DATABASE_URL: PostgreSQL connection string
 * - XERO_CLIENT_ID: Xero OAuth client ID
 * - XERO_CLIENT_SECRET: Xero OAuth client secret
 * - XERO_BASE_URL: Xero API base URL (optional, defaults to production)
 */
import { APIGatewayProxyEvent, Context, APIGatewayProxyResult, SQSEvent } from 'aws-lambda';
import { PrismaClient, Company } from '@prisma/client';

import axios from '../utils/axiosInstance';
import { refreshXeroToken } from '../services/refreshTokenService';
import { getXeroConfig } from '../config/environment';
import { XeroRequestData, ValidationError, XeroError, ProcessedReceiptData } from '../types';
import {
    startReceiptsExecution,
    completeReceiptsExecution,
    failReceiptsExecution,
    ReceiptsExecutionResult,
} from '../utils/receiptsLogger';
import {
    parseXeroDate,
    getCurrentSyncDate,
    generateXeroDateTime,
    formatDateForLogging,
} from '../utils/dateUtils';

// Production Configuration
const PRODUCTION_CONFIG = {
    API_TIMEOUT_MS: 30000,
    MAX_RETRIES: 3,
    RETRY_DELAY_MS: 1000,
};

// Get Prisma client
function getPrismaClient(): PrismaClient {
    return new PrismaClient({
        log: ['error', 'warn'],
        errorFormat: 'pretty',
    });
}

// Lambda handler
export const handler = async (
    event: APIGatewayProxyEvent | SQSEvent,
    context: Context
): Promise<APIGatewayProxyResult | void> => {
    if ('Records' in event && event.Records?.length) {
        // Handle SQS event - batch processing
        for (const record of event.Records) {
            try {
                const requestData = JSON.parse(record.body);
                await processRequest(requestData, context, 'SYSTEM');
            } catch (err) {
                console.error('❌ Failed to process SQS message', err);
                // Re-throw error to trigger SQS retry mechanism
                throw err;
            }
        }
        return;
    } else {
        // Handle API Gateway event - direct HTTP request
        try {
            const requestData = parseRequestData(event);
            await processRequest(requestData, context, 'USER');

            return {
                statusCode: 200,
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    success: true,
                    message: 'Receipts data processed successfully',
                    timestamp: new Date().toISOString(),
                }),
            };
        } catch (err: any) {
            console.error('❌ Handler error:', err);
            return {
                statusCode: err instanceof ValidationError ? 400 : 500,
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    success: false,
                    error: err.message,
                    type: err.constructor.name,
                }),
            };
        }
    }
};

// parse event
export function parseRequestData(
    event: APIGatewayProxyEvent | SQSEvent | { body?: string }
): XeroRequestData {
    // handle SQS
    if ('Records' in event && event.Records?.[0]) {
        try {
            const parsed = JSON.parse(event.Records[0].body);
            return parsed as XeroRequestData;
        } catch {
            throw new ValidationError('Failed to parse SQS message body');
        }
    }

    // handle API Gateway
    if ('body' in event && event.body) {
        try {
            const parsed = typeof event.body === 'string' ? JSON.parse(event.body) : event.body;
            return parsed as XeroRequestData;
        } catch {
            throw new ValidationError('Failed to parse API Gateway body');
        }
    }

    throw new ValidationError('Invalid request data');
}

// process request
async function processRequest(
    requestData: XeroRequestData,
    _context: Context,
    triggeredBy: 'USER' | 'SYSTEM' = 'SYSTEM'
): Promise<void> {
    const startTime = Date.now();
    let integrationLogId: string | null = null;
    let receiptsProcessed = 0;
    let receiptsInserted = 0;
    let receiptsUpdated = 0;

    try {
        // Start execution logging
        const { logId } = await startReceiptsExecution(requestData.companyId, triggeredBy);
        integrationLogId = logId;

        validateRequestData(requestData);

        const prisma = getPrismaClient();
        const integration = await getActiveIntegration(requestData, prisma);

        if (!integration) {
            throw new Error('Active integration not found');
        }

        const validIntegration = await ensureValidToken(integration);

        // Get last sync date for incremental sync
        const lastSyncDate = await getLastSyncDate(requestData.companyId, prisma);
        const syncStartTime = getCurrentSyncDate();

        console.log(`🚀 Fetching Receipts for company: ${requestData.companyId}`, lastSyncDate);

        // Create request data with UpdatedDateUTC filter for incremental sync
        if (lastSyncDate != null) {
            requestData = {
                ...requestData,
                where: `UpdatedDateUTC>=${generateXeroDateTime(lastSyncDate)}`
            };
        }

        // Fetch receipts from Xero with incremental sync
        const receiptsData = await getReceipts(
            validIntegration.XeroAccessToken!,
            validIntegration.XeroTenantId!,
            requestData
        );

        if (receiptsData && receiptsData.length > 0) {
            if (requestData.dumpToDatabase !== false) {
                const saveResult = await saveReceiptsToDatabase(
                    receiptsData,
                    requestData.companyId,
                    prisma
                );
                receiptsProcessed = receiptsData.length;
                receiptsInserted = saveResult.inserted;
                receiptsUpdated = saveResult.updated;

                console.log(
                    `✅ Successfully processed ${receiptsProcessed} receipts (${receiptsInserted} inserted, ${receiptsUpdated} updated)`
                );
            } else {
                console.log('📊 Data fetched but not dumped to DB (dumpToDatabase is false)');
                receiptsProcessed = receiptsData.length;
            }
        } else {
            console.log('📭 No receipts found to process');
        }

        // Update sync timestamp
        if (requestData.dumpToDatabase !== false) {
            await updateLastSyncDate(requestData.companyId, syncStartTime, prisma);
        }

        // Complete execution logging
        if (integrationLogId) {
            const result: ReceiptsExecutionResult = {
                totalReceipts: receiptsProcessed,
                processedReceipts: receiptsProcessed,
                insertedReceipts: receiptsInserted,
                updatedReceipts: receiptsUpdated,
                errors: 0,
                warnings: 0,
                duration: `${Date.now() - startTime}ms`,
            };
            await completeReceiptsExecution(integrationLogId, startTime, result);
        }

    } catch (error: any) {
        console.error('❌ Process request error:', error);

        // Log execution failure
        if (integrationLogId) {
            const partialResult: Partial<ReceiptsExecutionResult> = {
                totalReceipts: receiptsProcessed,
                processedReceipts: receiptsProcessed,
                insertedReceipts: receiptsInserted,
                updatedReceipts: receiptsUpdated,
                errors: 1,
                warnings: 0,
            };
            await failReceiptsExecution(integrationLogId, startTime, error, partialResult);
        }

        throw error;
    }
}

// Validate request data
function validateRequestData(data: XeroRequestData): void {
    if (!data.companyId) {
        throw new ValidationError('companyId is required');
    }
}

// Get active integration
async function getActiveIntegration(data: XeroRequestData, prisma: PrismaClient): Promise<Company> {
    const integration = await prisma.company.findFirst({
        where: {
            Id: data.companyId,
        },
    });

    if (!integration) {
        throw new Error('Active integration not found for company');
    }

    return integration;
}

// Ensure valid token
async function ensureValidToken(integration: Company): Promise<Company> {
    if (!integration.XeroAccessToken) {
        throw new Error('No Xero access token found');
    }

    // Check if token is expired (with 5-minute buffer)
    const now = new Date();
    const tokenExpiry = integration.XeroTokenExpiry;
    const bufferTime = 5 * 60 * 1000; // 5 minutes in milliseconds

    if (!tokenExpiry || now.getTime() > (tokenExpiry.getTime() - bufferTime)) {
        console.log('🔄 Token expired or expiring soon, refreshing...');
        return await refreshXeroToken(integration);
    }

    return integration;
}

// Get last sync date for incremental sync
async function getLastSyncDate(companyId: string, prisma: PrismaClient): Promise<Date | null> {
    try {
        const syncRecord = await prisma.xeroModuleSync.findUnique({
            where: {
                CompanyId_ModuleName: {
                    CompanyId: companyId,
                    ModuleName: 'Receipts',
                },
            },
        });

        return syncRecord?.LastSyncTime || null;
    } catch (error) {
        console.error('❌ Error getting last sync date:', error);
        return null;
    }
}

// Update last sync date

async function updateLastSyncDate(companyId: string, syncDate: Date, prisma: PrismaClient): Promise<void> {
    try {
        await prisma.xeroModuleSync.upsert({
            where: {
                CompanyId_ModuleName: {
                    CompanyId: companyId,
                    ModuleName: 'Receipts'
                }
            },
            update: {
                LastSyncTime: syncDate,
                UpdatedAt: getCurrentSyncDate()
            },
            create: {
                Id: require('uuid').v4(),
                CompanyId: companyId,
                ModuleName: 'Receipts',
                LastSyncTime: syncDate,
                CreatedAt: getCurrentSyncDate(),
                UpdatedAt: getCurrentSyncDate()
            }
        });

        console.log(`📅 Updated last sync date to: ${formatDateForLogging(syncDate)}`);
    } catch (error) {
        console.error("Error updating last sync date:", error);
        // Don't throw here as this shouldn't fail the main sync process
    }
}
// Get receipts from Xero with improved error handling and timeout configuration
const getReceipts = async (
    accessToken: string,
    tenantId: string,
    requestData: XeroRequestData
): Promise<any[]> => {
    const startTime = Date.now();

    try {
        const config = getXeroConfig();
        const url = `${config.baseUrl}Receipts`;

        console.log(`🌐 API Request URL: ${url.replace(accessToken, '[REDACTED]')}`, requestData);
        console.log('Request Data:', tenantId);

        const response = await axios.get(url, {
            headers: {
                Authorization: `Bearer ${accessToken}`,
                'Xero-tenant-id': tenantId,
                Accept: 'application/json',
                'User-Agent': 'ReceiptsSync/1.0.0',
            },
            params: requestData.where ? { where: requestData.where } : {},
            timeout: PRODUCTION_CONFIG.API_TIMEOUT_MS, // Configurable API timeout
        });

        const requestTime = Date.now() - startTime;
        console.log(`✅ Receipts API call completed in ${requestTime}ms`);

        const receiptsData = response?.data;

        if (!receiptsData || !receiptsData.Receipts) {
            throw new Error('Invalid Receipts data structure received from Xero');
        }

        console.log(`📊 Retrieved ${receiptsData.Receipts.length} receipts from Xero`);
        return receiptsData.Receipts;
    } catch (error: any) {
        console.error(error);
        const requestTime = Date.now() - startTime;
        console.error(`❌ Receipts API call failed after ${requestTime}ms:`, {
            error: error.message,
            status: error.response?.status,
            statusText: error.response?.statusText,
            tenantId: tenantId.substring(0, 8) + '...',
        });

        // Handle specific Xero API errors
        if (error.response?.status === 429) {
            throw new XeroError(`Rate limit exceeded. Please try again later.`, 429, error);
        }

        if (error.response?.status === 401) {
            throw new XeroError(`Authentication failed. Token may be expired.`, 401, error);
        }

        if (error.response?.status === 403) {
            throw new XeroError(`Access forbidden. Check your Xero permissions.`, 403, error);
        }

        if (error.response?.status === 404) {
            throw new XeroError(`Receipts endpoint not found.`, 404, error);
        }

        if (error.code === 'ECONNABORTED') {
            throw new XeroError(`Request timeout after ${PRODUCTION_CONFIG.API_TIMEOUT_MS}ms`, 408, error);
        }

        // Generic error
        throw new XeroError(
            `Failed to fetch Receipts: ${error.message}`,
            error.response?.status || 500,
            error
        );
    }
};
//  save receipts to database
export async function saveReceiptsToDatabase(
    receipts: ProcessedReceiptData[],
    companyId: string,
    prisma: PrismaClient
): Promise<{ inserted: number; updated: number }> {
    console.log(`Saving ${receipts.length} receipts to database`);

    const receiptRecords = receipts.map(receipt => ({
        ReceiptsID: receipt.ReceiptID,
        ReceiptNumber: receipt.ReceiptNumber?.toString() || null,
        Status: receipt.Status || null,
        UserID: receipt.User?.UserID || null,
        FirstName: receipt.User?.FirstName || null,
        LastName: receipt.User?.LastName || null,
        ContactID: receipt.Contact?.ContactID || null,
        ContactName: receipt.Contact?.Name || null,
        Date: parseXeroDate(receipt.Date) || null,
        Reference: receipt.Reference || null,
        LineAmountTypes: receipt.LineAmountTypes || null,
        SubTotal: receipt.SubTotal ?? null,
        TotalTax: receipt.TotalTax ?? null,
        Total: receipt.Total ?? null,
        HasAttachments: receipt.HasAttachments ?? null,
        UpdateUTCDate: parseXeroDate(receipt.UpdatedDateUTC) || new Date(),
        CompanyId: companyId,
    }));

    const receiptIds = receiptRecords.map(r => r.ReceiptsID);

    const existingReceipts = await prisma.receipt.findMany({
        where: { ReceiptsID: { in: receiptIds } },
        select: { ReceiptsID: true },
    });
    const existingReceiptIds = new Set(existingReceipts.map(r => r.ReceiptsID));

    const receiptsToInsert = receiptRecords.filter(r => !existingReceiptIds.has(r.ReceiptsID));
    const receiptsToUpdate = receiptRecords.filter(r => existingReceiptIds.has(r.ReceiptsID));

    if (receiptsToInsert.length > 0) {
        await prisma.receipt.createMany({ data: receiptsToInsert, skipDuplicates: true });
        console.log(`📥 Inserted ${receiptsToInsert.length} new receipts`);
    }

    if (receiptsToUpdate.length > 0) {
        await prisma.$transaction(
            receiptsToUpdate.map(receipt =>
                prisma.receipt.update({
                    where: { ReceiptsID: receipt.ReceiptsID },
                    data: receipt,
                })
            )
        );
        console.log(`🔄 Updated ${receiptsToUpdate.length} existing receipts`);
    }

    return {
        inserted: receiptsToInsert.length,
        updated: receiptsToUpdate.length,
    };
}