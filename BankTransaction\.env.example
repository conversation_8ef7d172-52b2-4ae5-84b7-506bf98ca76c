# Prisma
DATABASE_URL=postgresql://username:password@host:port/database?schema=public

# AWS
REGION=us-east-1

# XERO OAuth
XERO_CLIENT_ID=your-xero-client-id
XERO_CLIENT_SECRET=your-xero-client-secret
XERO_REDIRECT_URI=http://localhost:8000/xero/callback
XERO_TOKEN_URL=https://identity.xero.com/connect/token
XERO_BASE_URL=https://api.xero.com/api.xro/2.0/

# Retry Configuration (in minutes)
FIRST_RETRY=10
SECOND_RETRY=20
LAST_RETRY=30

# Optional Headers / Keys
LOG_SERVICE_API_KEY=your-log-service-api-key
LOG_SERVICE_URL=https://your-log-service-url.com/logs

ACCESS_KEY_ID=your-access-key-id
SECRET_ACCESS_KEY=your-secret-access-key
IS_OFFLINE=true
