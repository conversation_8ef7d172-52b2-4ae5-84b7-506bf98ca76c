import { EnvironmentConfig } from '../types';

export function getEnvironmentConfig(): EnvironmentConfig {
  const requiredEnvVars = ['DATABASE_URL', 'XERO_CLIENT_ID', 'XERO_CLIENT_SECRET'];

  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      throw new Error(`Required environment variable ${envVar} is not set`);
    }
  }

  return {
    DATABASE_URL: process.env['DATABASE_URL']!,
    XERO_CLIENT_ID: process.env['XERO_CLIENT_ID']!,
    XERO_CLIENT_SECRET: process.env['XERO_CLIENT_SECRET']!,
    XERO_TOKEN_URL: process.env['XERO_TOKEN_URL'] || '',
    XERO_BASE_URL: process.env['XERO_BASE_URL'] || '',
    FIRST_RETRY: process.env['FIRST_RETRY'] || '',
    SECOND_RETRY: process.env['SECOND_RETRY'] || '',
    LAST_RETRY: process.env['LAST_RETRY'] || '',
    REGION: process.env['REGION'] || '',
    ACCESS_KEY_ID: process.env['ACCESS_KEY_ID'] || '',
    SECRET_ACCESS_KEY: process.env['SECRET_ACCESS_KEY'] || '',
    IS_OFFLINE: process.env['IS_OFFLINE'] || '',
    PRISMA_QUERY_ENGINE_LIBRARY: process.env['PRISMA_QUERY_ENGINE_LIBRARY'] || '',
    AWS_LAMBDA_FUNCTION_NAME: process.env['AWS_LAMBDA_FUNCTION_NAME'] || '',
  };
}

export function isOfflineMode(): boolean {
  return process.env['IS_OFFLINE'] === 'true';
}

export function getXeroConfig() {
  const config = getEnvironmentConfig();
  return {
    clientId: config.XERO_CLIENT_ID,
    clientSecret: config.XERO_CLIENT_SECRET,
    tokenUrl: config.XERO_TOKEN_URL || 'https://identity.xero.com/connect/token',
    baseUrl: config.XERO_BASE_URL || 'https://api.xero.com/api.xro/2.0/',
  };
}

export function getAWSConfig() {
  const config = getEnvironmentConfig();
  return {
    region: config.REGION || 'us-east-1',
    accessKeyId: config.ACCESS_KEY_ID,
    secretAccessKey: config.SECRET_ACCESS_KEY,
  };
}
