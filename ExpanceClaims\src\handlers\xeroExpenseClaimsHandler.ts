/**
 * Xero ExpenseClaims Data Synchronization Handler
 *
 * This AWS Lambda handler synchronizes ExpenseClaim data from Xero API.
 *
 * Key Features:
 * - Bulk insert and update of expense claims
 * - Handles both API Gateway and SQS events
 * - Automatic Xero token refresh
 * - Comprehensive error handling and logging
 * - Incremental sync support
 *
 * Usage:
 * - API Gateway: POST /expense-claims/sync with { companyId: "uuid" }
 * - SQS: Queue message with { companyId: "uuid", tenantId?: "string" }
 *
 * Environment Variables Required:
 * - DATABASE_URL: PostgreSQL connection string
 * - XERO_CLIENT_ID: Xero OAuth client ID
 * - XERO_CLIENT_SECRET: Xero OAuth client secret
 * - XERO_BASE_URL: Xero API base URL (optional, defaults to production)
 */
import { APIGatewayProxyEvent, Context, APIGatewayProxyResult, SQSEvent } from 'aws-lambda';
import { PrismaClient, Company } from '@prisma/client';

import axios from '../utils/axiosInstance';
import { refreshXeroToken } from '../services/refreshTokenService';
import { getXeroConfig } from '../config/environment';
import { XeroRequestData, ValidationError, XeroError, RawExpenseClaim } from '../types';
import {
    startExpenseClaimsExecution,
    completeExpenseClaimsExecution,
    failExpenseClaimsExecution,
    ExpenseClaimsExecutionResult,
} from '../utils/expenseClaimsLogger';
import {
    parseXeroDate,
    getCurrentSyncDate,
    generateXeroDateTime,
    formatDateForLogging,
} from '../utils/dateUtils';

// Production Configuration
const PRODUCTION_CONFIG = {
    API_TIMEOUT_MS: 30000,
    MAX_RETRIES: 3,
    RETRY_DELAY_MS: 1000,
};

// Get Prisma client
function getPrismaClient(): PrismaClient {
    return new PrismaClient({
        log: ['error', 'warn'],
        errorFormat: 'pretty',
    });
}

// Lambda handler
export const handler = async (
    event: APIGatewayProxyEvent | SQSEvent,
    context: Context
): Promise<APIGatewayProxyResult | void> => {
    if ('Records' in event && event.Records?.length) {
        // Handle SQS event - batch processing
        for (const record of event.Records) {
            try {
                const requestData = JSON.parse(record.body);
                await processRequest(requestData, context, 'SYSTEM');
            } catch (err) {
                console.error('❌ Failed to process SQS message', err);
                // Re-throw error to trigger SQS retry mechanism
                throw err;
            }
        }
        return;
    } else {
        // Handle API Gateway event - direct HTTP request
        try {
            const requestData = parseRequestData(event);
            await processRequest(requestData, context, 'USER');

            return {
                statusCode: 200,
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    success: true,
                    message: 'ExpenseClaims data processed successfully',
                    timestamp: new Date().toISOString(),
                }),
            };
        } catch (err: any) {
            console.error('❌ Handler error:', err);
            return {
                statusCode: err instanceof ValidationError ? 400 : 500,
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    success: false,
                    error: err.message,
                    type: err.constructor.name,
                }),
            };
        }
    }
};

// parse event
export function parseRequestData(
    event: APIGatewayProxyEvent | SQSEvent | { body?: string }
): XeroRequestData {
    // handle SQS
    if ('Records' in event && event.Records?.[0]) {
        try {
            const parsed = JSON.parse(event.Records[0].body);
            return parsed as XeroRequestData;
        } catch {
            throw new ValidationError('Failed to parse SQS message body');
        }
    }

    // handle API Gateway
    if ('body' in event && event.body) {
        try {
            const parsed = typeof event.body === 'string' ? JSON.parse(event.body) : event.body;
            return parsed as XeroRequestData;
        } catch {
            throw new ValidationError('Failed to parse API Gateway body');
        }
    }

    throw new ValidationError('Invalid request data');
}

// process request
async function processRequest(
    requestData: XeroRequestData,
    _context: Context,
    triggeredBy: 'USER' | 'SYSTEM' = 'SYSTEM'
): Promise<void> {
    const startTime = Date.now();
    let integrationLogId: string | null = null;
    let expenseClaimsProcessed = 0;
    let expenseClaimsInserted = 0;
    let expenseClaimsUpdated = 0;

    try {
        // Start execution logging
        const { logId } = await startExpenseClaimsExecution(requestData.companyId, triggeredBy);
        integrationLogId = logId;

        validateRequestData(requestData);

        const prisma = getPrismaClient();
        const integration = await getActiveIntegration(requestData, prisma);

        if (!integration) {
            throw new Error('Active integration not found');
        }

        const validIntegration = await ensureValidToken(integration);

        // Get last sync date for incremental sync
        const lastSyncDate = await getLastSyncDate(requestData.companyId, prisma);
        const syncStartTime = getCurrentSyncDate();

        console.log(`🚀 Fetching ExpenseClaims for company: ${requestData.companyId}`, lastSyncDate);

        // Create request data with UpdatedDateUTC filter for incremental sync
        if (lastSyncDate != null) {
            requestData = {
                ...requestData,
                where: `UpdatedDateUTC>=${generateXeroDateTime(lastSyncDate)}`
            };
        }

        // Fetch expense claims from Xero with incremental sync
        const expenseClaimsData = await getExpenseClaims(
            validIntegration.XeroAccessToken!,
            validIntegration.XeroTenantId!,
            requestData
        );

        if (expenseClaimsData && expenseClaimsData.length > 0) {
            if (requestData.dumpToDatabase !== false) {
                const saveResult = await saveExpenseClaimsToDatabase(
                    expenseClaimsData,
                    requestData.companyId,
                    prisma
                );
                expenseClaimsProcessed = expenseClaimsData.length;
                expenseClaimsInserted = saveResult.inserted;
                expenseClaimsUpdated = saveResult.updated;

                console.log(
                    `✅ Successfully processed ${expenseClaimsProcessed} expense claims (${expenseClaimsInserted} inserted, ${expenseClaimsUpdated} updated)`
                );
            } else {
                console.log('📊 Data fetched but not dumped to DB (dumpToDatabase is false)');
                expenseClaimsProcessed = expenseClaimsData.length;
            }
        } else {
            console.log('📭 No expense claims found to process');
        }

        // Update sync timestamp
        if (requestData.dumpToDatabase !== false) {
            await updateLastSyncDate(requestData.companyId, syncStartTime, prisma);
        }

        // Complete execution logging
        if (integrationLogId) {
            const result: ExpenseClaimsExecutionResult = {
                totalExpenseClaims: expenseClaimsProcessed,
                processedExpenseClaims: expenseClaimsProcessed,
                insertedExpenseClaims: expenseClaimsInserted,
                updatedExpenseClaims: expenseClaimsUpdated,
                errors: 0,
                warnings: 0,
                duration: `${Date.now() - startTime}ms`,
            };
            await completeExpenseClaimsExecution(integrationLogId, startTime, result);
        }

    } catch (error: any) {
        console.error('❌ Process request error:', error);

        // Log execution failure
        if (integrationLogId) {
            const partialResult: Partial<ExpenseClaimsExecutionResult> = {
                totalExpenseClaims: expenseClaimsProcessed,
                processedExpenseClaims: expenseClaimsProcessed,
                insertedExpenseClaims: expenseClaimsInserted,
                updatedExpenseClaims: expenseClaimsUpdated,
                errors: 1,
                warnings: 0,
            };
            await failExpenseClaimsExecution(integrationLogId, startTime, error, partialResult);
        }

        throw error;
    }
}

// Validate request data
function validateRequestData(data: XeroRequestData): void {
    if (!data.companyId) {
        throw new ValidationError('companyId is required');
    }
}

// Get active integration
async function getActiveIntegration(data: XeroRequestData, prisma: PrismaClient): Promise<Company> {
    const integration = await prisma.company.findFirst({
        where: {
            Id: data.companyId,
        },
    });

    if (!integration) {
        throw new Error('Active integration not found for company');
    }

    return integration;
}

// Ensure valid token
async function ensureValidToken(integration: Company): Promise<Company> {
    if (!integration.XeroAccessToken) {
        throw new Error('No Xero access token found');
    }

    // Check if token is expired (with 5-minute buffer)
    const now = new Date();
    const tokenExpiry = integration.XeroTokenExpiry;
    const bufferTime = 5 * 60 * 1000; // 5 minutes in milliseconds

    if (!tokenExpiry || now.getTime() > (tokenExpiry.getTime() - bufferTime)) {
        console.log('🔄 Token expired or expiring soon, refreshing...');
        return await refreshXeroToken(integration);
    }

    return integration;
}

// Get last sync date for incremental sync
async function getLastSyncDate(companyId: string, prisma: PrismaClient): Promise<Date | null> {
    try {
        const syncRecord = await prisma.xeroModuleSync.findUnique({
            where: {
                CompanyId_ModuleName: {
                    CompanyId: companyId,
                    ModuleName: 'ExpenseClaims',
                },
            },
        });

        return syncRecord?.LastSyncTime || null;
    } catch (error) {
        console.error('❌ Error getting last sync date:', error);
        return null;
    }
}

// Update last sync date

async function updateLastSyncDate(companyId: string, syncDate: Date, prisma: PrismaClient): Promise<void> {
    try {
        await prisma.xeroModuleSync.upsert({
            where: {
                CompanyId_ModuleName: {
                    CompanyId: companyId,
                    ModuleName: 'ExpenseClaims'
                }
            },
            update: {
                LastSyncTime: syncDate,
                UpdatedAt: getCurrentSyncDate()
            },
            create: {
                Id: require('uuid').v4(),
                CompanyId: companyId,
                ModuleName: 'ExpenseClaims',
                LastSyncTime: syncDate,
                CreatedAt: getCurrentSyncDate(),
                UpdatedAt: getCurrentSyncDate()
            }
        });

        console.log(`📅 Updated last sync date to: ${formatDateForLogging(syncDate)}`);
    } catch (error) {
        console.error("Error updating last sync date:", error);
        // Don't throw here as this shouldn't fail the main sync process
    }
}
// Get expense claims from Xero with improved error handling and timeout configuration
const getExpenseClaims = async (
    accessToken: string,
    tenantId: string,
    requestData: XeroRequestData
): Promise<any[]> => {
    const startTime = Date.now();

    try {
        const config = getXeroConfig();
        const url = `${config.baseUrl}ExpenseClaims`;

        console.log(`🌐 API Request URL: ${url.replace(accessToken, '[REDACTED]')}`, requestData);
        console.log('Request Data:', tenantId);

        const response = await axios.get(url, {
            headers: {
                Authorization: `Bearer ${accessToken}`,
                'Xero-tenant-id': tenantId,
                Accept: 'application/json',
                'User-Agent': 'ExpenseClaimsSync/1.0.0',
            },
            params: requestData.where ? { where: requestData.where } : {},
            timeout: PRODUCTION_CONFIG.API_TIMEOUT_MS, // Configurable API timeout
        });

        const requestTime = Date.now() - startTime;
        console.log(`✅ ExpenseClaims API call completed in ${requestTime}ms`);

        const expenseClaimsData = response?.data;

        if (!expenseClaimsData || !expenseClaimsData.ExpenseClaims) {
            throw new Error('Invalid ExpenseClaims data structure received from Xero');
        }

        console.log(`📊 Retrieved ${expenseClaimsData.ExpenseClaims.length} expense claims from Xero`);
        return expenseClaimsData.ExpenseClaims;
    } catch (error: any) {
        console.error(error);
        const requestTime = Date.now() - startTime;
        console.error(`❌ Employees API call failed after ${requestTime}ms:`, {
            error: error.message,
            status: error.response?.status,
            statusText: error.response?.statusText,
            tenantId: tenantId.substring(0, 8) + '...',
        });

        // Handle specific Xero API errors
        if (error.response?.status === 429) {
            throw new XeroError(`Rate limit exceeded. Please try again later.`, 429, error);
        }

        if (error.response?.status === 401) {
            throw new XeroError(`Authentication failed. Token may be expired.`, 401, error);
        }

        if (error.response?.status === 403) {
            throw new XeroError(`Access forbidden. Check your Xero permissions.`, 403, error);
        }

        if (error.response?.status === 404) {
            throw new XeroError(`Employees endpoint not found.`, 404, error);
        }

        if (error.code === 'ECONNABORTED') {
            throw new XeroError(`Request timeout after ${PRODUCTION_CONFIG.API_TIMEOUT_MS}ms`, 408, error);
        }

        // Generic error
        throw new XeroError(
            `Failed to fetch employees: ${error.message}`,
            error.response?.status || 500,
            error
        );
    }
};
//  save expense claims to database

export async function saveExpenseClaimsToDatabase(
    claims: RawExpenseClaim[],
    companyId: string,
    prisma: PrismaClient
): Promise<{ inserted: number; updated: number }> {
    console.log(`Saving ${claims.length} expense claims to database`);

    // 1. Normalize data to match Prisma model
    const claimRecords = claims.map(claim => ({
        ExpenseClaimID: claim.ExpenseClaimID,
        Status: claim.Status || null,
        UserID: claim.User?.UserID || null,
        EmailAddress: claim.User?.EmailAddress || null,
        FirstName: claim.User?.FirstName || null,
        LastName: claim.User?.LastName || null,
        UserUpdatedDateUTC: parseXeroDate(claim.User?.UpdatedDateUTC) || null,
        IsSubscriber: claim.User?.IsSubscriber ?? null,
        OrganisationRole: claim.User?.OrganisationRole || null,
        Total: claim.Total ?? null,
        AmountDue: claim.AmountDue ?? null,
        AmountPaid: claim.AmountPaid ?? null,
        PaymentDueDate: parseXeroDate(claim.PaymentDueDate) || null,
        ReportingDate: parseXeroDate(claim.ReportingDate) || null,
        UpdateUTCDate: parseXeroDate(claim.UpdatedDateUTC) || new Date(),
        CompanyId: companyId,
    }));

    const claimIds = claimRecords.map(c => c.ExpenseClaimID);

    // 2. Find existing claims
    const existingClaims = await prisma.expenseClaim.findMany({
        where: { ExpenseClaimID: { in: claimIds } },
        select: { ExpenseClaimID: true },
    });

    const existingClaimIds = new Set(existingClaims.map(c => c.ExpenseClaimID));

    // 3. Split into inserts and updates
    const claimsToInsert = claimRecords.filter(c => !existingClaimIds.has(c.ExpenseClaimID));
    const claimsToUpdate = claimRecords.filter(c => existingClaimIds.has(c.ExpenseClaimID));

    // 4. Insert new claims
    if (claimsToInsert.length > 0) {
        await prisma.expenseClaim.createMany({ data: claimsToInsert, skipDuplicates: true });
        console.log(`📥 Inserted ${claimsToInsert.length} new expense claims`);
    }

    // 5. Update existing claims
    if (claimsToUpdate.length > 0) {
        await prisma.$transaction(
            claimsToUpdate.map(claim =>
                prisma.expenseClaim.update({
                    where: { ExpenseClaimID: claim.ExpenseClaimID },
                    data: claim,
                })
            )
        );
        console.log(`🔄 Updated ${claimsToUpdate.length} existing expense claims`);
    }

    return {
        inserted: claimsToInsert.length,
        updated: claimsToUpdate.length,
    };
}