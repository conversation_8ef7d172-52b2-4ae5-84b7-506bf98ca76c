/**
 * Xero Bank Transactions Data Synchronization Handler
 *
 * This AWS Lambda handler synchronizes Bank Transactions data from Xero API.
 *
 * Key Features:
 * - Bulk insert and update of bank transactions and line items
 * - Handles both API Gateway and SQS events
 * - Automatic Xero token refresh
 * - Comprehensive error handling and logging
 * - Pagination support for large datasets
 * - Incremental sync using UpdatedDateUTC
 *
 * Usage:
 * - API Gateway: POST /banktransactions/sync with { companyId: "uuid" }
 * - SQS: Queue message with { companyId: "uuid", tenantId?: "string" }
 *
 * Environment Variables Required:
 * - DATABASE_URL: PostgreSQL connection string
 * - XERO_CLIENT_ID: Xero OAuth client ID
 * - XERO_CLIENT_SECRET: Xero OAuth client secret
 * - XERO_REDIRECT_URI: Xero OAuth redirect URI
 * - XERO_BASE_URL: Xero API base URL (optional, defaults to production)
 */
import { APIGatewayProxyEvent, Context, APIGatewayProxyResult, SQSEvent } from 'aws-lambda';
import { PrismaClient, Company } from '@prisma/client';

import axios from '../utils/axiosInstance';
import { refreshXeroToken } from '../services/refreshTokenService';
import { getXeroConfig, PRODUCTION_CONFIG } from '../config/environment';
import { XeroRequestData, ValidationError, XeroError, BankTransactionExecutionResult } from '../types';
import {
    startBankTransactionsExecution,
    logBankTransactionsSuccess,
    logBankTransactionsFailure,
} from '../utils/bankTransactionsLogger';
import {
    formatDateForXeroAPI,
    getDefaultSyncStartDate,
    getCurrentSyncDate,
    formatDateForLogging,
    generateXeroDateTime,
} from '../utils/dateUtils';

import dotenv from 'dotenv';
dotenv.config();

// Prisma
let prisma: PrismaClient | null = null;
function getPrismaClient(): PrismaClient {
    if (!prisma) {
        const config = {
            log: ['error', 'warn'] as Array<'error' | 'warn'>,
            errorFormat: 'pretty' as const,
        };
        if (process.env['IS_OFFLINE'] === 'true') {
            delete process.env['PRISMA_QUERY_ENGINE_LIBRARY'];
        }
        prisma = new PrismaClient(config);
    }
    return prisma;
}

// Lambda handler
export const handler = async (
    event: APIGatewayProxyEvent | SQSEvent,
    context: Context
): Promise<APIGatewayProxyResult | void> => {
    if ('Records' in event && event.Records?.length) {
        // Handle SQS event - batch processing
        for (const record of event.Records) {
            try {
                const requestData = JSON.parse(record.body);
                await processRequest(requestData, context, 'SYSTEM');
            } catch (err) {
                console.error('❌ Failed to process SQS message', err);
                // Re-throw error to trigger SQS retry mechanism
                throw err;
            }
        }
        return;
    } else {
        // Handle API Gateway event - direct HTTP request
        try {
            const requestData = parseRequestData(event);
            await processRequest(requestData, context, 'USER');

            return {
                statusCode: 200,
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    success: true,
                    message: 'Bank Transactions data processed successfully',
                    timestamp: new Date().toISOString(),
                }),
            };
        } catch (err: any) {
            console.error('❌ Handler error:', err);
            return {
                statusCode: err instanceof ValidationError ? 400 : 500,
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    success: false,
                    error: err.message,
                    type: err.constructor.name,
                }),
            };
        }
    }
};

// parse event
export function parseRequestData(
    event: APIGatewayProxyEvent | SQSEvent | { body?: string }
): XeroRequestData {
    // handle SQS
    if ('Records' in event && event.Records?.[0]) {
        try {
            const parsed = JSON.parse(event.Records[0].body);
            return parsed as XeroRequestData;
        } catch {
            throw new ValidationError('Failed to parse SQS message body');
        }
    }

    // handle API Gateway
    if ('body' in event && event.body) {
        try {
            const parsed = typeof event.body === 'string' ? JSON.parse(event.body) : event.body;
            return parsed as XeroRequestData;
        } catch {
            throw new ValidationError('Failed to parse API Gateway body');
        }
    }

    throw new ValidationError('Invalid request data');
}

// process request
async function processRequest(
    requestData: XeroRequestData,
    _context: Context,
    triggeredBy: 'USER' | 'SYSTEM' = 'SYSTEM'
): Promise<void> {
    const startTime = Date.now();
    let integrationLogId: string | null = null;
    let bankTransactionsProcessed = 0;
    let bankTransactionsInserted = 0;
    let bankTransactionsUpdated = 0;
    let bankTransactionLinesInserted = 0;
    let bankTransactionLinesUpdated = 0;

    try {
        // Start execution logging
        const { logId } = await startBankTransactionsExecution(requestData.companyId, triggeredBy);
        integrationLogId = logId;

        validateRequestData(requestData);

        const prisma = getPrismaClient();
        const integration = await getActiveIntegration(requestData, prisma);

        if (!integration) {
            throw new Error('Active integration not found');
        }

        const validIntegration = await ensureValidToken(integration);

        // Get last sync date for incremental sync
        const lastSyncDate = await getLastSyncDate(requestData.companyId, prisma);
        const syncStartTime = getCurrentSyncDate();

        console.log(`🚀 Fetching Bank Transactions for company: ${requestData.companyId}`, lastSyncDate);

        // Create request data with UpdatedDateUTC filter for incremental sync
        if (lastSyncDate != null) {
            requestData = {
                ...requestData,
                where: `UpdatedDateUTC>=${generateXeroDateTime(lastSyncDate)}`
            };
        }

        // Fetch bank transactions from Xero with pagination
        const allBankTransactions = await getAllBankTransactions(
            validIntegration.XeroAccessToken!,
            validIntegration.XeroTenantId!,
            requestData
        );

        if (allBankTransactions && allBankTransactions.length > 0) {
            if (requestData.dumpToDatabase !== false) {
                const saveResult = await saveBankTransactionsToDatabase(
                    allBankTransactions,
                    requestData.companyId,
                    prisma
                );
                bankTransactionsProcessed = allBankTransactions.length;
                bankTransactionsInserted = saveResult.bankTransactionsInserted;
                bankTransactionsUpdated = saveResult.bankTransactionsUpdated;
                bankTransactionLinesInserted = saveResult.bankTransactionLinesInserted;
                bankTransactionLinesUpdated = saveResult.bankTransactionLinesUpdated;

                console.log(
                    `✅ Successfully processed ${bankTransactionsProcessed} bank transactions (${bankTransactionsInserted} inserted, ${bankTransactionsUpdated} updated) and ${bankTransactionLinesInserted + bankTransactionLinesUpdated} line items`
                );
            } else {
                console.log('📊 Data fetched but not dumped to DB (dumpToDatabase is false)');
                bankTransactionsProcessed = allBankTransactions.length;
            }
        } else {
            console.log('📭 No bank transactions found to process');
        }

        // Log success
        if (integrationLogId) {
            const executionResult: BankTransactionExecutionResult = {
                totalBankTransactions: allBankTransactions?.length || 0,
                processedBankTransactions: bankTransactionsProcessed,
                insertedBankTransactions: bankTransactionsInserted,
                updatedBankTransactions: bankTransactionsUpdated,
                insertedBankTransactionLines: bankTransactionLinesInserted,
                updatedBankTransactionLines: bankTransactionLinesUpdated,
                errors: 0,
                warnings: 0,
                duration: `${Date.now() - startTime}ms`,
            };

            await logBankTransactionsSuccess(integrationLogId, startTime, executionResult);

            // Update last sync date for incremental sync
            await updateLastSyncDate(requestData.companyId, syncStartTime, prisma);
        }

        const totalDuration = Date.now() - startTime;
        console.log(`✅ Bank Transactions sync completed successfully in ${(totalDuration / 1000).toFixed(1)}s`);
    } catch (error: any) {
        // Log failure
        if (integrationLogId) {
            await logBankTransactionsFailure(integrationLogId, startTime, error, {
                totalBankTransactions: 0,
                processedBankTransactions: bankTransactionsProcessed,
                insertedBankTransactions: bankTransactionsInserted,
                updatedBankTransactions: bankTransactionsUpdated,
                insertedBankTransactionLines: bankTransactionLinesInserted,
                updatedBankTransactionLines: bankTransactionLinesUpdated,
                errors: 1,
                warnings: 0,
                duration: `${Date.now() - startTime}ms`,
            });
        }

        const totalDuration = Date.now() - startTime;
        console.error(`❌ Bank Transactions sync failed after ${(totalDuration / 1000).toFixed(1)}s:`, {
            error: error.message,
            companyId: requestData.companyId,
        });
        throw error;
    }
}

// Validate request data
function validateRequestData(data: XeroRequestData): void {
    if (!data.companyId) {
        throw new ValidationError('companyId is required');
    }
}

// Get active integration
async function getActiveIntegration(data: XeroRequestData, prisma: PrismaClient): Promise<Company> {
    const integration = await prisma.company.findFirst({
        where: {
            Id: data.companyId,
            ConnectionStatus: 'ACTIVE',
        },
    });

    if (!integration) {
        throw new Error('Active integration not found for company');
    }

    return integration;
}

// Ensure valid token
async function ensureValidToken(integration: Company): Promise<Company> {
    if (!integration.XeroAccessToken || !integration.XeroTokenExpiry) {
        throw new Error('No access token found');
    }

    const now = new Date();
    const expiry = new Date(integration.XeroTokenExpiry);
    // Add configurable buffer before token expiry
    if (expiry.getTime() - now.getTime() <= PRODUCTION_CONFIG.TOKEN_REFRESH_BUFFER_MS) {
        console.log('Token expired or about to expire, refreshing...');
        return await refreshXeroToken(integration);
    }

    return integration;
}

// Get last sync date from xeroModuleSync table
async function getLastSyncDate(companyId: string, prisma: PrismaClient): Promise<any> {
    try {
        const lastSync: any = await prisma.xeroModuleSync.findFirst({
            where: {
                CompanyId: companyId,
                ModuleName: 'BankTransactions'
            },
            orderBy: {
                LastSyncTime: 'desc'
            }
        });

        if (lastSync) {
            return lastSync?.LastSyncTime;
        }

    } catch (error) {
        console.error("Error getting last sync date:", error);
        const defaultDate = getDefaultSyncStartDate();
        console.log(`📅 Using default date due to error: ${formatDateForLogging(defaultDate)}`);
        return defaultDate;
    }
}

// Update last sync date in xeroModuleSync table
async function updateLastSyncDate(companyId: string, syncDate: Date, prisma: PrismaClient): Promise<void> {
    try {
        await prisma.xeroModuleSync.upsert({
            where: {
                CompanyId_ModuleName: {
                    CompanyId: companyId,
                    ModuleName: 'BankTransactions'
                }
            },
            update: {
                LastSyncTime: syncDate,
                UpdatedAt: getCurrentSyncDate()
            },
            create: {
                Id: require('uuid').v4(),
                CompanyId: companyId,
                ModuleName: 'BankTransactions',
                LastSyncTime: syncDate,
                CreatedAt: getCurrentSyncDate(),
                UpdatedAt: getCurrentSyncDate()
            }
        });

        console.log(`📅 Updated last sync date to: ${formatDateForLogging(syncDate)}`);
    } catch (error) {
        console.error("Error updating last sync date:", error);
        // Don't throw here as this shouldn't fail the main sync process
    }
}

// Get all bank transactions from Xero with pagination support
const getAllBankTransactions = async (
    accessToken: string,
    tenantId: string,
    requestData: XeroRequestData
): Promise<any[]> => {
    const allBankTransactions: any[] = [];
    let page = 1;
    let hasMorePages = true;

    while (hasMorePages) {
        console.log(`🌐 Fetching Bank Transactions page ${page}...`);

        const bankTransactions = await getBankTransactions(
            accessToken,
            tenantId,
            { ...requestData, page }
        );

        if (bankTransactions && bankTransactions.length > 0) {
            allBankTransactions.push(...bankTransactions);
            console.log(`📊 Retrieved ${bankTransactions.length} bank transactions from page ${page}`);

            // Check if we have more pages (Xero returns up to 100 records per page)
            if (bankTransactions.length < PRODUCTION_CONFIG.DEFAULT_PAGE_SIZE) {
                hasMorePages = false;
            } else {
                page++;
                // Add delay between API calls to avoid rate limiting
                await new Promise(resolve => setTimeout(resolve, PRODUCTION_CONFIG.XERO_API_DELAY_MS));
            }
        } else {
            hasMorePages = false;
        }
    }

    console.log(`📊 Total Bank Transactions retrieved: ${allBankTransactions.length}`);
    return allBankTransactions;
};

// Get bank transactions from Xero with improved error handling and timeout configuration
const getBankTransactions = async (
    accessToken: string,
    tenantId: string,
    requestData: XeroRequestData
): Promise<any[]> => {
    const startTime = Date.now();

    try {
        const config = getXeroConfig();
        const url = `${config.baseUrl}BankTransactions`;

        console.log(`🌐 API Request URL: ${url.replace(accessToken, '[REDACTED]')}`, requestData);

        const params: any = {};
        if (requestData.where) {
            params.where = requestData.where;
        }
        if (requestData.page) {
            params.page = requestData.page;
        }

        const response = await axios.get(url, {
            headers: {
                Authorization: `Bearer ${accessToken}`,
                'Xero-tenant-id': tenantId,
                Accept: 'application/json',
                'User-Agent': 'BankTransactionsSync/1.0.0',
            },
            params,
            timeout: PRODUCTION_CONFIG.API_TIMEOUT_MS, // Configurable API timeout
        });

        const requestTime = Date.now() - startTime;
        console.log(`✅ Bank Transactions API call completed in ${requestTime}ms`);

        const bankTransactionsData = response?.data;
        if (!bankTransactionsData || !bankTransactionsData.BankTransactions) {
            throw new Error('Invalid Bank Transactions data structure received from Xero');
        }

        console.log(`📊 Retrieved ${bankTransactionsData.BankTransactions.length} bank transactions from Xero`);
        return bankTransactionsData.BankTransactions;
    } catch (error: any) {
        console.error(error);
        const requestTime = Date.now() - startTime;
        console.error(`❌ Bank Transactions API call failed after ${requestTime}ms:`, {
            error: error.message,
            status: error.response?.status,
            statusText: error.response?.statusText,
            tenantId: tenantId.substring(0, 8) + '...',
        });

        // Handle specific Xero API errors
        if (error.response?.status === 429) {
            throw new XeroError(`Rate limit exceeded. Please try again later.`, 429, error);
        }

        if (error.response?.status === 401) {
            throw new XeroError(`Authentication failed. Token may be expired.`, 401, error);
        }

        if (error.response?.status === 403) {
            throw new XeroError(`Access forbidden. Check tenant permissions.`, 403, error);
        }

        // Extract Xero-specific error message if available
        const xeroErrorMessage =
            error.response?.data?.Elements?.[0]?.ValidationErrors?.[0]?.Message ||
            error.response?.data?.message ||
            error.message;

        throw new XeroError(
            `Failed to fetch bank transactions: ${xeroErrorMessage}`,
            error.response?.status,
            error
        );
    }
};

// Helper function to parse Xero's .NET date format
function parseXeroDate(dateString: string | null | undefined): Date | null {
    if (!dateString || typeof dateString !== 'string') return null;

    // Handle Xero's .NET date format: "/Date(*************+0000)/"
    const match = dateString.match(/\/Date\((\d+)([+-]\d{4})\)\//);
    if (match) {
        const timestamp = parseInt(match[1] || '0');
        return new Date(timestamp);
    }

    // Try parsing as regular date string
    const parsedDate = new Date(dateString);
    return isNaN(parsedDate.getTime()) ? null : parsedDate;
}

// Save bank transactions to database
async function saveBankTransactionsToDatabase(
    bankTransactions: any[],
    companyId: string,
    prisma: PrismaClient
): Promise<{
    bankTransactionsInserted: number;
    bankTransactionsUpdated: number;
    bankTransactionLinesInserted: number;
    bankTransactionLinesUpdated: number;
}> {
    console.log(`Saving ${bankTransactions.length} bank transactions to database`);

    // 1. Map all bank transactions to DB shape
    const bankTransactionRecords = bankTransactions.map(bt => ({
        BankTransactionID: bt.BankTransactionID,
        Type: bt.Type || null,
        AccountID: bt.BankAccount?.AccountID || null,
        BankAccountNumber: bt.BankAccount?.Code || null,
        Code: bt.BankAccount?.Code || null,
        BankAccountName: bt.BankAccount?.Name || null,
        ContactID: bt.Contact?.ContactID || null,
        ContactName: bt.Contact?.Name || null,
        ContactFirstName: null, // Not provided in the API response
        ContactLastName: null, // Not provided in the API response
        CurrencyCode: bt.CurrencyCode || null,
        Date: parseXeroDate(bt.Date) || parseXeroDate(bt.DateString) || null,
        DueDate: null, // Not applicable for bank transactions
        FullyPaidOnDate: null, // Not applicable for bank transactions
        IsReconciled: bt.IsReconciled || false,
        LineAmountTypes: bt.LineAmountTypes || null,
        Reference: bt.Reference || null,
        Status: bt.Status || null,
        SubTotal: bt.SubTotal ? parseFloat(bt.SubTotal.toString()) : null,
        TotalTax: bt.TotalTax ? parseFloat(bt.TotalTax.toString()) : null,
        Total: bt.Total ? parseFloat(bt.Total.toString()) : null,
        TotalMovement: null, // Not provided in the API response
        UpdateUTCDate: parseXeroDate(bt.UpdatedDateUTC) || new Date(),
        CompanyId: companyId,
        CurrencyRate: null, // Not provided in the API response
    }));

    const bankTransactionIds = bankTransactionRecords.map(bt => bt.BankTransactionID);

    // 2. Fetch existing BankTransactionIDs
    const existingBankTransactions = await prisma.bankTransaction.findMany({
        where: { BankTransactionID: { in: bankTransactionIds } },
        select: { BankTransactionID: true },
    });
    const existingBankTransactionIds = new Set(existingBankTransactions.map(bt => bt.BankTransactionID));

    // 3. Split into new and existing
    const bankTransactionsToInsert = bankTransactionRecords.filter(bt => !existingBankTransactionIds.has(bt.BankTransactionID));
    const bankTransactionsToUpdate = bankTransactionRecords.filter(bt => existingBankTransactionIds.has(bt.BankTransactionID));

    // 4. Bulk insert new bank transactions
    if (bankTransactionsToInsert.length > 0) {
        await prisma.bankTransaction.createMany({ data: bankTransactionsToInsert, skipDuplicates: true });
        console.log(`📥 Inserted ${bankTransactionsToInsert.length} new bank transactions`);
    }

    // 5. Bulk update existing bank transactions
    if (bankTransactionsToUpdate.length > 0) {
        await prisma.$transaction(
            bankTransactionsToUpdate.map(bankTransaction =>
                prisma.bankTransaction.update({
                    where: { BankTransactionID: bankTransaction.BankTransactionID },
                    data: bankTransaction,
                })
            )
        );
        console.log(`🔄 Updated ${bankTransactionsToUpdate.length} existing bank transactions`);
    }

    // 6. Process bank transaction line items
    const allLineItems: any[] = [];
    bankTransactions.forEach(bt => {
        if (bt.LineItems && Array.isArray(bt.LineItems)) {
            bt.LineItems.forEach((lineItem: any) => {
                allLineItems.push({
                    ...lineItem,
                    BankTransactionID: bt.BankTransactionID,
                    CompanyId: companyId,
                });
            });
        }
    });

    let bankTransactionLinesInserted = 0;
    let bankTransactionLinesUpdated = 0;

    if (allLineItems.length > 0) {
        const lineItemResult = await saveBankTransactionLinesToDatabase(allLineItems, companyId, prisma);
        bankTransactionLinesInserted = lineItemResult.inserted;
        bankTransactionLinesUpdated = lineItemResult.updated;
    }

    return {
        bankTransactionsInserted: bankTransactionsToInsert.length,
        bankTransactionsUpdated: bankTransactionsToUpdate.length,
        bankTransactionLinesInserted,
        bankTransactionLinesUpdated,
    };
}

// Save bank transaction lines to database
async function saveBankTransactionLinesToDatabase(
    lineItems: any[],
    companyId: string,
    prisma: PrismaClient
): Promise<{ inserted: number; updated: number }> {
    console.log(`Saving ${lineItems.length} bank transaction line items to database`);

    // 1. Map all line items to DB shape
    const lineItemRecords = lineItems.map(item => ({
        Id: require('uuid').v4(), // Generate new UUID for each line item
        BankTransactionId: item.BankTransactionID,
        LineItemId: item.LineItemID,
        ContactId: null, // Not provided in the API response
        ContactName: null, // Not provided in the API response
        Date: null, // Not provided in the API response
        Reference: null, // Not provided in the API response
        Description: item.Description || null,
        Quantity: item.Quantity ? parseFloat(item.Quantity.toString()) : null,
        UnitAmount: item.UnitAmount ? parseFloat(item.UnitAmount.toString()) : null,
        AccountCode: item.AccountCode || null,
        TaxAmount: item.TaxAmount ? parseFloat(item.TaxAmount.toString()) : null,
        LineAmount: item.LineAmount ? parseFloat(item.LineAmount.toString()) : null,
        TaxType: item.TaxType || null,
        TrackingCategory1: null, // Would need to parse from Tracking array
        TrackingCategory1Value: null, // Would need to parse from Tracking array
        TrackingCategory2: null, // Would need to parse from Tracking array
        TrackingCategory2Value: null, // Would need to parse from Tracking array
        CurrencyCode: null, // Not provided in the API response
        CurrencyRate: null, // Not provided in the API response
        CompanyId: companyId,
    }));

    // 2. Delete existing line items for these bank transactions to avoid duplicates
    const bankTransactionIds = [...new Set(lineItems.map(item => item.BankTransactionID))];
    await prisma.bankTransactionLine.deleteMany({
        where: {
            BankTransactionId: { in: bankTransactionIds },
        },
    });

    // 3. Insert all line items
    if (lineItemRecords.length > 0) {
        await prisma.bankTransactionLine.createMany({
            data: lineItemRecords,
            skipDuplicates: true
        });
        console.log(`📥 Inserted ${lineItemRecords.length} bank transaction line items`);
    }

    return {
        inserted: lineItemRecords.length,
        updated: 0, // We delete and recreate, so no updates
    };
}
