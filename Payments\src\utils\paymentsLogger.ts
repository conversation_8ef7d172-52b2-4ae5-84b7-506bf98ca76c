/*
 * Payments Logger Utility
 *
 * This utility provides logging functionality for
 * Payments Lambda function execution using the IntegrationLog model.
 *
 * Key Features:
 * - Lambda function execution logging (success/failure)
 * - Integration with existing IntegrationLog model
 * - Functional implementation
 * - Comprehensive error handling
 */

import {
  logLambdaInvocation,
  updateLambdaSuccess,
  updateLambdaError,
  formatDuration,
} from './integrationLogger';

/**
 * Payments Execution Result Interface
 */
export interface PaymentsExecutionResult {
  totalPayments: number;
  processedPayments: number;
  insertedPayments: number;
  updatedPayments: number;
  errors: number;
  warnings: number;
  duration: string;
}

/**
 * Start Payments Lambda execution logging
 */
export async function startPaymentsExecution(
  companyId: string,
  triggeredBy: 'USER' | 'SYSTEM' = 'SYSTEM'
): Promise<{ logId: string; startTime: number }> {
  try {
    const startTime = Date.now();
    const logId = await logLambdaInvocation(companyId, 'Payments', triggeredBy);
    return { logId, startTime };
  } catch (error) {
    console.error('❌ Failed to start Payments execution logging:', error);
    throw error;
  }
}

/**
 * Log Payments execution success
 */
export async function logPaymentsSuccess(
  logId: string,
  startTime: number,
  result: PaymentsExecutionResult
): Promise<void> {
  try {
    const duration = formatDuration(Date.now() - startTime);
    const message = `Payments sync successful. Processed ${result.processedPayments} payments (${result.insertedPayments} inserted, ${result.updatedPayments} updated).`;
    const syncSummary = {
      totalPayments: result.totalPayments,
      processedPayments: result.processedPayments,
      insertedPayments: result.insertedPayments,
      updatedPayments: result.updatedPayments,
      duration: result.duration,
      errors: result.errors,
      warnings: result.warnings,
    };
    await updateLambdaSuccess(logId, duration, message, syncSummary);
  } catch (logError) {
    console.error('❌ Failed to log Payments success:', logError);
    // Don't throw here to avoid masking the original success
  }
}

/**
 * Log Payments execution failure
 */
export async function logPaymentsFailure(
  logId: string,
  startTime: number,
  error: Error,
  partialResult?: Partial<PaymentsExecutionResult>
): Promise<void> {
  try {
    const duration = formatDuration(Date.now() - startTime);
    const partialSummary = {
      totalPayments: partialResult?.totalPayments || 0,
      processedPayments: partialResult?.processedPayments || 0,
      insertedPayments: partialResult?.insertedPayments || 0,
      updatedPayments: partialResult?.updatedPayments || 0,
      duration: partialResult?.duration || duration,
      errors: 1,
      warnings: partialResult?.warnings || 0,
    };
    await updateLambdaError(logId, duration, error, partialSummary);
  } catch (logError) {
    console.error('❌ Failed to log Payments failure:', logError);
    // Don't throw here to avoid masking the original error
  }
}

/**
 * Wrapper function for complete Payments execution with logging
 */
export async function executeWithPaymentsLogging<T>(
  companyId: string,
  operation: () => Promise<T>,
  createResult: (data: T, duration: string) => PaymentsExecutionResult,
  triggeredBy: 'USER' | 'SYSTEM' = 'SYSTEM'
): Promise<T> {
  const startTime = Date.now();
  let logId: string | null = null;
  try {
    // Start execution logging
    const logInfo = await startPaymentsExecution(companyId, triggeredBy);
    logId = logInfo.logId;
    // Execute the operation
    const result = await operation();
    // Calculate duration
    const duration = formatDuration(Date.now() - startTime);
    // Create execution result
    const executionResult = createResult(result, duration);
    // Log success
    await logPaymentsSuccess(logId, startTime, executionResult);
    return result;
  } catch (error) {
    // Calculate duration
    const duration = formatDuration(Date.now() - startTime);
    // Log failure
    if (logId) {
      await logPaymentsFailure(logId, startTime, error as Error, {
        duration,
        errors: 1,
      });
    }
    throw error;
  }
} 