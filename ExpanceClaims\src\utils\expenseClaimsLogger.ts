/**
 * ExpenseClaims Logger Utility
 *
 * This utility provides simplified logging functionality specifically for
 * ExpenseClaims Lambda function execution using only the IntegrationLog model.
 * It follows the same pattern as Accounts logging.
 *
 * Key Features:
 * - Lambda function execution logging (success/failure)
 * - Integration with existing IntegrationLog model
 * - Functional implementation
 * - Comprehensive error handling
 *
 * <AUTHOR> Logger Utility
 * @version 1.0.0
 */

import {
  logLambdaInvocation,
  updateLambdaSuccess,
  updateLambdaError,
  formatDuration,
} from './integrationLogger';

/**
 * ExpenseClaims Execution Result Interface
 */
export interface ExpenseClaimsExecutionResult {
  totalExpenseClaims: number;
  processedExpenseClaims: number;
  insertedExpenseClaims: number;
  updatedExpenseClaims: number;
  errors: number;
  warnings: number;
  duration: string;
}

/**
 * Start ExpenseClaims Lambda execution logging
 */
export async function startExpenseClaimsExecution(
  companyId: string,
  triggeredBy: 'USER' | 'SYSTEM' = 'SYSTEM'
): Promise<{ logId: string; startTime: number }> {
  try {
    const startTime = Date.now();
    const logId = await logLambdaInvocation(companyId, 'ExpenseClaims', triggeredBy);

    return { logId, startTime };
  } catch (error) {
    console.error('❌ Failed to start ExpenseClaims execution logging:', error);
    throw error;
  }
}

/**
 * Complete ExpenseClaims Lambda execution with success
 */
export async function completeExpenseClaimsExecution(
  logId: string,
  startTime: number,
  result: ExpenseClaimsExecutionResult
): Promise<void> {
  try {
    const executionTime = (Date.now() - startTime).toString();

    const summary = {
      totalExpenseClaims: result.totalExpenseClaims,
      processedExpenseClaims: result.processedExpenseClaims,
      insertedExpenseClaims: result.insertedExpenseClaims,
      updatedExpenseClaims: result.updatedExpenseClaims,
      errors: result.errors,
      warnings: result.warnings,
      executionTime: formatDuration(executionTime),
      successRate: result.totalExpenseClaims > 0 ?
        ((result.processedExpenseClaims - result.errors) / result.totalExpenseClaims * 100).toFixed(2) + '%' : '0%',
    };

    await updateLambdaSuccess(logId, executionTime, "ExpenseClaims Sync successful", summary);

    console.log('📊 ExpenseClaims Execution Summary:', {
      ...summary,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('❌ Failed to complete ExpenseClaims execution logging:', error);
    // Don't throw here to avoid masking the original success
  }
}

/**
 * Complete ExpenseClaims Lambda execution with error
 */
export async function failExpenseClaimsExecution(
  logId: string,
  startTime: number,
  error: any,
  partialResult?: Partial<ExpenseClaimsExecutionResult>
): Promise<void> {
  try {
    const executionTime = (Date.now() - startTime).toString();

    const summary = {
      totalExpenseClaims: partialResult?.totalExpenseClaims || 0,
      processedExpenseClaims: partialResult?.processedExpenseClaims || 0,
      insertedExpenseClaims: partialResult?.insertedExpenseClaims || 0,
      updatedExpenseClaims: partialResult?.updatedExpenseClaims || 0,
      errors: (partialResult?.errors || 0) + 1, // +1 for the current error
      warnings: partialResult?.warnings || 0,
      executionTime: formatDuration(executionTime),
      errorType: error.constructor.name,
      errorMessage: error.message,
    };

    await updateLambdaError(logId, executionTime, error, summary);

    console.error('💥 ExpenseClaims Execution Failed:', {
      ...summary,
      timestamp: new Date().toISOString(),
    });
  } catch (logError) {
    console.error('❌ Failed to log ExpenseClaims execution error:', logError);
    // Don't throw here to avoid masking the original error
  }
}
