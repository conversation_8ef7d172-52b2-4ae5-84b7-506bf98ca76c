/**
 * Bank Transactions Logger Utility
 *
 * This utility provides simplified logging functionality specifically for
 * Bank Transactions Lambda function execution using only the IntegrationLog model.
 * It follows the same pattern as Accounts logging.
 *
 * Key Features:
 * - Lambda function execution logging (success/failure)
 * - Integration with existing IntegrationLog model
 * - Functional implementation
 * - Comprehensive error handling
 *
 * <AUTHOR> Transactions Logger Utility
 * @version 1.0.0
 */

import {
  logLambdaInvocation,
  updateLambdaSuccess,
  updateLambdaError,
  formatDuration,
} from './integrationLogger';
import { BankTransactionExecutionResult } from '../types';

/**
 * Start Bank Transactions Lambda execution logging
 */
export async function startBankTransactionsExecution(
  companyId: string,
  triggeredBy: 'USER' | 'SYSTEM' = 'SYSTEM'
): Promise<{ logId: string; startTime: number }> {
  try {
    const startTime = Date.now();
    const logId = await logLambdaInvocation(companyId, 'BankTransactions', triggeredBy);

    return { logId, startTime };
  } catch (error) {
    console.error('❌ Failed to start Bank Transactions execution logging:', error);
    throw error;
  }
}

/**
 * Log Bank Transactions execution success
 */
export async function logBankTransactionsSuccess(
  logId: string,
  startTime: number,
  result: BankTransactionExecutionResult
): Promise<void> {
  try {
    const duration = formatDuration(Date.now() - startTime);
    const message = `Bank Transactions sync successful.`;

    const syncSummary = {
      totalBankTransactions: result.totalBankTransactions,
      processedBankTransactions: result.processedBankTransactions,
      insertedBankTransactions: result.insertedBankTransactions,
      updatedBankTransactions: result.updatedBankTransactions,
      insertedBankTransactionLines: result.insertedBankTransactionLines,
      updatedBankTransactionLines: result.updatedBankTransactionLines,
      duration: result.duration,
      errors: result.errors,
      warnings: result.warnings,
    };

    await updateLambdaSuccess(logId, duration, message, syncSummary);
  } catch (logError) {
    console.error('❌ Failed to log Bank Transactions success:', logError);
    // Don't throw here to avoid masking the original success
  }
}

/**
 * Log Bank Transactions execution failure
 */
export async function logBankTransactionsFailure(
  logId: string,
  startTime: number,
  error: any,
  partialSummary: Partial<BankTransactionExecutionResult>
): Promise<void> {
  try {
    const duration = formatDuration(Date.now() - startTime);
    const errorMessage = error?.message || 'Unknown error';
    const errorDetails = {
      name: error?.name || 'Error',
      message: errorMessage,
      stack: error?.stack,
      code: error?.code,
      statusCode: error?.statusCode,
    };

    await updateLambdaError(logId, duration, error, partialSummary);
  } catch (logError) {
    console.error('❌ Failed to log Bank Transactions failure:', logError);
    // Don't throw here to avoid masking the original error
  }
}

/**
 * Wrapper function for complete Bank Transactions execution with logging
 */
export async function executeWithLogging<T>(
  companyId: string,
  operation: () => Promise<T>,
  createResult: (data: T, duration: string) => BankTransactionExecutionResult,
  triggeredBy: 'USER' | 'SYSTEM' = 'SYSTEM'
): Promise<T> {
  const startTime = Date.now();
  let logId: string | null = null;

  try {
    // Start execution logging
    const { logId: newLogId } = await startBankTransactionsExecution(companyId, triggeredBy);
    logId = newLogId;

    // Execute the operation
    const result = await operation();

    // Log success
    const duration = formatDuration(Date.now() - startTime);
    const executionResult = createResult(result, duration);
    await logBankTransactionsSuccess(logId, startTime, executionResult);

    return result;
  } catch (error) {
    // Log failure
    if (logId) {
      await logBankTransactionsFailure(logId, startTime, error, {
        totalBankTransactions: 0,
        processedBankTransactions: 0,
        insertedBankTransactions: 0,
        updatedBankTransactions: 0,
        insertedBankTransactionLines: 0,
        updatedBankTransactionLines: 0,
        errors: 1,
        warnings: 0,
        duration: formatDuration(Date.now() - startTime),
      });
    }

    throw error;
  }
}
