service: xero-trackingcategories

frameworkVersion: '4'

provider:
  name: aws
  runtime: nodejs18.x
  region: us-east-1
  memorySize: 1024
  architecture: x86_64
  tracing:
    lambda: true
  timeout: 500
  environment:
    DATABASE_URL: ${env:DATABASE_URL}
    XERO_CLIENT_ID: ${env:XERO_CLIENT_ID}
    XERO_CLIENT_SECRET: ${env:XERO_CLIENT_SECRET}
    XERO_TOKEN_URL: ${env:XERO_TOKEN_URL}
    XERO_BASE_URL: ${env:XERO_BASE_URL}
    FIRST_RETRY: ${env:FIRST_RETRY}
    SECOND_RETRY: ${env:SECOND_RETRY}
    LAST_RETRY: ${env:LAST_RETRY}
    REGION: ${env:REGION}
    ACCESS_KEY_ID: ${env:ACCESS_KEY_ID}
    SECRET_ACCESS_KEY: ${env:SECRET_ACCESS_KEY}
    IS_OFFLINE: ${env:IS_OFFLINE, 'false'}
    PRISMA_QUERY_ENGINE_LIBRARY: /var/task/node_modules/.prisma/client/libquery_engine-rhel-openssl-1.0.x.so.node

functions:
  xeroHandler:
    handler: src/handlers/xeroTrackingCategoriesHandler.handler
    environment:
      IS_OFFLINE: ${env:IS_OFFLINE, 'true'}
    events:
      - sqs:
          arn:
            Fn::GetAtt: [TrackingCategoriesSyncQueue, Arn]
          batchSize: 5
      - http:
          path: xero/sync-trackingcategories
          method: post

package:
  patterns:
    - '!node_modules/.prisma/client/libquery_engine-darwin*'
    - '!node_modules/.prisma/client/libquery_engine-windows*'
    - '!node_modules/.prisma/client/libquery_engine-arm*'
    - '!node_modules/.prisma/client/libquery_engine-debian*'
    - '!node_modules/.prisma/client/libquery_engine-rhel-openssl-3.0.x*'
    - '!node_modules/@prisma/engines/**'
    - 'node_modules/.prisma/**'
    - 'src/**'
    - 'node_modules/**'
    - '!node_modules/prisma/**'

plugins:
  - serverless-offline

custom:
  serverless-offline:
    httpPort: 3005
  dotenv:
    path: .env
  serverless-offline-sqs:
    autoCreate: true
    apiVersion: '2012-11-05'
    endpoint: http://0.0.0.0:9324
    region: us-east-1
    accessKeyId: root
    secretAccessKey: root
    skipCacheInvalidation: false
    queues:
      - name: trackingcategories-sync-queue
        arn:
          Fn::GetAtt: [TrackingCategoriesSyncQueue, Arn]
