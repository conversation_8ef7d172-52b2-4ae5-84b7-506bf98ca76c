/**
 * Xero Budgets Data Synchronization Handler
 *
 * This AWS Lambda handler synchronizes Budgets data from Xero API.
 *
 * Key Features:
 * - Bulk insert and update of budgets
 * - Handles both API Gateway and SQS events
 * - Automatic Xero token refresh
 * - Comprehensive error handling and logging
 *
 * Usage:
 * - API Gateway: POST /budgets/sync with { companyId: "uuid" }
 * - SQS: Queue message with { companyId: "uuid", tenantId?: "string" }
 *
 * Environment Variables Required:
 * - DATABASE_URL: PostgreSQL connection string
 * - XERO_CLIENT_ID: Xero OAuth client ID
 * - XERO_CLIENT_SECRET: Xero OAuth client secret
 * - XERO_REDIRECT_URI: Xero OAuth redirect URI
 * - XERO_BASE_URL: Xero API base URL (optional, defaults to production)
 */
import { APIGatewayProxyEvent, Context, APIGatewayProxyResult, SQSEvent } from 'aws-lambda';
import { PrismaClient, Company, Account } from '@prisma/client';

import axios from '../utils/axiosInstance';
import { refreshXeroToken } from '../services/refreshTokenService';
import { getXeroConfig } from '../config/environment';
import {
    XeroRequestData,
    ValidationError,
    XeroError,
} from '../types';

import dotenv from 'dotenv';
dotenv.config();

// Prisma
let prisma: PrismaClient | null = null;
function getPrismaClient(): PrismaClient {
    if (!prisma) {
        const config = {
            log: ['error', 'warn'] as Array<'error' | 'warn'>,
            errorFormat: 'pretty' as const,
        };
        if (process.env.IS_OFFLINE === 'true') {
            delete process.env.PRISMA_QUERY_ENGINE_LIBRARY;
        }
        prisma = new PrismaClient(config);
    }
    return prisma;
}

// Lambda handler
export const handler = async (
    event: APIGatewayProxyEvent | SQSEvent,
    context: Context
): Promise<APIGatewayProxyResult | void> => {
    if ("Records" in event && event.Records?.length) {
        // SQS
        for (const record of event.Records) {
            try {
                const requestData = JSON.parse(record.body);
                await processRequest(requestData, context);
            } catch (err) {
                console.error("Failed to process SQS message", err);
                throw err; // let Lambda/SQS retry
            }
        }
        return;
    } else {
        // API Gateway
        try {
            const requestData = parseRequestData(event);
            await processRequest(requestData, context);

            return {
                statusCode: 200,
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({
                    success: true,
                    message: "Budgets data processed successfully",
                    timestamp: new Date().toISOString(),
                }),
            };
        } catch (err: any) {
            console.error("Handler error:", err);
            return {
                statusCode: err instanceof ValidationError ? 400 : 500,
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({
                    success: false,
                    error: err.message,
                    type: err.constructor.name,
                }),
            };
        }
    }
};

// parse event
export function parseRequestData(
    event: APIGatewayProxyEvent | SQSEvent | { body?: string }
): XeroRequestData {
    // handle SQS
    if ("Records" in event && event.Records?.[0]) {
        try {
            const parsed = JSON.parse(event.Records[0].body);
            return parsed as XeroRequestData;
        } catch {
            throw new ValidationError("Failed to parse SQS message body");
        }
    }

    // handle API Gateway
    if ("body" in event && event.body) {
        try {
            const parsed =
                typeof event.body === "string" ? JSON.parse(event.body) : event.body;
            return parsed as XeroRequestData;
        } catch {
            throw new ValidationError("Failed to parse API Gateway body");
        }
    }

    throw new ValidationError("Invalid request data");
}

// process request
async function processRequest(
    requestData: XeroRequestData,
    _context: Context
): Promise<void> {
    try {
        validateRequestData(requestData);

        const prisma = getPrismaClient();
        const integration = await getActiveIntegration(requestData, prisma);

        if (!integration) {
            throw new Error("Active integration not found");
        }

        const validIntegration = await ensureValidToken(integration);

        console.log(`🚀 Fetching Budgets for company: ${requestData.companyId}`);

        // Fetch budgets from Xero
        const budgetsData = await getBudgets(
            validIntegration.XeroAccessToken!,
            validIntegration.XeroTenantId!,
            requestData
        );

        if (budgetsData && budgetsData.length > 0) {
            if (requestData.dumpToDatabase) {
                await saveBudgetsToDatabase(budgetsData, requestData.companyId, prisma);
                console.log(`✅ Successfully processed ${budgetsData.length} budgets`);
            } else {
                console.log("Data fetched but not dumped to DB (dumpToDatabase is false)");
            }
        } else {
            console.log("No budgets found to process");
        }

    } catch (error) {
        console.error("Error processing budgets request:", error);
        throw error;
    }
}

// Validate request data
function validateRequestData(data: XeroRequestData): void {
    if (!data.companyId) {
        throw new ValidationError("companyId is required");
    }
}

// Get active integration
async function getActiveIntegration(data: XeroRequestData, prisma: PrismaClient): Promise<Company> {
    const integration = await prisma.company.findFirst({
        where: {
            Id: data.companyId,
            ConnectionStatus: "ACTIVE",
        },
    });

    if (!integration) {
        throw new Error("Active integration not found for company");
    }

    return integration;
}

// Ensure valid token
async function ensureValidToken(integration: Company): Promise<Company> {
    if (!integration.XeroAccessToken || !integration.XeroTokenExpiry) {
        throw new Error("No access token found");
    }

    const now = new Date();
    const expiry = new Date(integration.XeroTokenExpiry);
    // Add a 10 minute buffer (600,000 ms)
    if (expiry.getTime() - now.getTime() <= 10 * 60 * 1000) {
        console.log("Token expired or about to expire, refreshing...");
        return await refreshXeroToken(integration);
    }

    return integration;
}

// Get budgets from Xero
const getBudgets = async (
    accessToken: string,
    tenantId: string,
    requestData: XeroRequestData
): Promise<any[]> => {
    try {
        const config = getXeroConfig();
        const url = `${config.baseUrl}/Budgets`;
        
        console.log(`Fetching budgets from Xero API: ${url}`);

        // Build query params
        const params: any = {};
        if (requestData.where) params.where = requestData.where;
        if (requestData.page) params.page = requestData.page;
        if (requestData.pageSize) params.pageSize = requestData.pageSize;

        const response = await axios.get(url, {
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Xero-tenant-id': tenantId,
                'Accept': 'application/json'
            },
            params,
        });

        // Log the full Xero response for debugging
        console.log('Xero Budgets API raw response:', JSON.stringify(response.data, null, 2));

        if (response.data && response.data.Budgets) {
            console.log(`Retrieved ${response.data.Budgets.length} budgets from Xero`);
            return response.data.Budgets;
        }

        return [];
    } catch (error: any) {
        console.error("Error fetching budgets from Xero:", error.response?.data || error.message);
        throw new XeroError(`Failed to fetch budgets: ${error.response?.data?.Elements?.[0]?.ValidationErrors?.[0]?.Message || error.message}`);
    }
};

// Helper function to parse Xero's .NET date format
function parseXeroDate(dateString: string | null | undefined): Date | null {
    if (!dateString || typeof dateString !== 'string') return null;
    
    // Handle Xero's .NET date format: "/Date(1751362260967+0000)/"
    const match = dateString.match(/\/Date\((\d+)([+-]\d{4})\)\//);
    if (match) {
        const timestamp = parseInt(match[1] || "0");
        return new Date(timestamp);
    }
    
    // Try parsing as regular date string
    const parsedDate = new Date(dateString);
    return isNaN(parsedDate.getTime()) ? null : parsedDate;
}

// Map Xero budget to Prisma budget data
function mapXeroBudgetToPrismaBudget(budget: any, companyId: string): any {
    return {
        BudgetID: budget.BudgetID,
        BudgetType: budget.Type || null,
        Description: budget.Description || null,
        Trackings: Array.isArray(budget.Tracking) ? budget.Tracking.length : null,
        UpdateUTCDate: parseXeroDate(budget.UpdatedDateUTC)?.toISOString() || new Date().toISOString(),
        CompanyId: companyId
    };
}

// Save budgets to database
async function saveBudgetsToDatabase(
    budgets: any[],
    companyId: string,
    prisma: PrismaClient
): Promise<void> {
    console.log(`Saving ${budgets.length} budgets to database`);

    // 1. Map all budgets to DB shape
    const budgetRecords = budgets.map(budget => ({
        BudgetID: budget.BudgetID,
        BudgetType: budget.Type || null,
        Description: budget.Description || null,
        Trackings: Array.isArray(budget.Tracking) ? budget.Tracking.length : null,
        UpdateUTCDate: parseXeroDate(budget.UpdatedDateUTC) || new Date(),
        CompanyId: companyId
    }));
    const budgetIds = budgetRecords.map(b => b.BudgetID);

    // 2. Fetch existing BudgetIDs
    const existingBudgets = await prisma.budget.findMany({
        where: { BudgetID: { in: budgetIds } },
        select: { BudgetID: true }
    });
    const existingBudgetIds = new Set(existingBudgets.map(b => b.BudgetID));

    // 3. Split into new and existing
    const budgetsToInsert = budgetRecords.filter(b => !existingBudgetIds.has(b.BudgetID));
    const budgetsToUpdate = budgetRecords.filter(b => existingBudgetIds.has(b.BudgetID));

    // 4. Bulk insert new budgets
    if (budgetsToInsert.length > 0) {
        await prisma.budget.createMany({ data: budgetsToInsert, skipDuplicates: true });
        console.log(`Inserted ${budgetsToInsert.length} new budgets`);
    }
    // 5. Bulk update existing budgets
    if (budgetsToUpdate.length > 0) {
        await prisma.$transaction(
            budgetsToUpdate.map(budget =>
                prisma.budget.update({
                    where: { BudgetID: budget.BudgetID },
                    data: budget
                })
            )
        );
        console.log(`Updated ${budgetsToUpdate.length} existing budgets`);
    }
}
