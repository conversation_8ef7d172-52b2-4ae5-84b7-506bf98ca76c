/**
 * Xero Invoices Data Synchronization Handler
 *
 * This AWS Lambda handler synchronizes Invoices and their line items from Xero API.
 *
 * Key Features:
 * - Bulk insert, update, and delete of invoices and their lines
 * - Handles both API Gateway and SQS events
 * - Automatic Xero token refresh
 * - Comprehensive error handling and logging
 *
 * Usage:
 * - API Gateway: POST /invoices/sync with { companyId: "uuid" }
 * - SQS: Queue message with { companyId: "uuid", tenantId?: "string" }
 *
 * Environment Variables Required:
 * - DATABASE_URL: PostgreSQL connection string
 * - XERO_CLIENT_ID: Xero OAuth client ID
 * - XERO_CLIENT_SECRET: Xero OAuth client secret
 * - XERO_REDIRECT_URI: Xero OAuth redirect URI
 * - XERO_BASE_URL: Xero API base URL (optional, defaults to production)
 */
import { APIGatewayProxyEvent, Context, APIGatewayProxyResult, SQSEvent } from 'aws-lambda';
import { PrismaClient, Company } from '@prisma/client';
import axios from '../utils/axiosInstance';
import { refreshXeroToken } from '../services/refreshTokenService';
import { getXeroConfig } from '../config/environment';
import {
    XeroRequestData,
    ValidationError,
    XeroError,
} from '../types';
import dotenv from 'dotenv';
dotenv.config();

let prisma: PrismaClient | null = null;
function getPrismaClient(): PrismaClient {
    if (!prisma) {
        const config = {
            log: ['error', 'warn'] as Array<'error' | 'warn'>,
            errorFormat: 'pretty' as const,
        };
        if (process.env.IS_OFFLINE === 'true') {
            delete process.env.PRISMA_QUERY_ENGINE_LIBRARY;
        }
        prisma = new PrismaClient(config);
    }
    return prisma;
}

export const handler = async (
    event: APIGatewayProxyEvent | SQSEvent,
    context: Context
): Promise<APIGatewayProxyResult | void> => {
    if ("Records" in event && event.Records?.length) {
        for (const record of event.Records) {
            try {
                const requestData = JSON.parse(record.body);
                await processRequest(requestData, context);
            } catch (err) {
                console.error("Failed to process SQS message", err);
                throw err;
            }
        }
        return;
    } else {
        try {
            const requestData = parseRequestData(event);
            await processRequest(requestData, context);
            return {
                statusCode: 200,
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({
                    success: true,
                    message: "Invoices data processed successfully",
                    timestamp: new Date().toISOString(),
                }),
            };
        } catch (err: any) {
            console.error("Handler error:", err);
            return {
                statusCode: err instanceof ValidationError ? 400 : 500,
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({
                    success: false,
                    error: err.message,
                    type: err.constructor.name,
                }),
            };
        }
    }
};

export function parseRequestData(
    event: APIGatewayProxyEvent | SQSEvent | { body?: string }
): XeroRequestData {
    if ("Records" in event && event.Records?.[0]) {
        try {
            const parsed = JSON.parse(event.Records[0].body);
            return parsed as XeroRequestData;
        } catch {
            throw new ValidationError("Failed to parse SQS message body");
        }
    }
    if ("body" in event && event.body) {
        try {
            const parsed =
                typeof event.body === "string" ? JSON.parse(event.body) : event.body;
            return parsed as XeroRequestData;
        } catch {
            throw new ValidationError("Failed to parse API Gateway body");
        }
    }
    throw new ValidationError("Invalid request data");
}

async function processRequest(
    requestData: XeroRequestData,
    _context: Context
): Promise<void> {
    try {
        validateRequestData(requestData);
        const prisma = getPrismaClient();
        const integration = await getActiveIntegration(requestData, prisma);
        if (!integration) {
            throw new Error("Active integration not found");
        }
        const validIntegration = await ensureValidToken(integration);
        console.log(`🚀 Fetching Invoices for company: ${requestData.companyId}`);
        const invoicesData = await getInvoices(
            validIntegration.XeroAccessToken!,
            validIntegration.XeroTenantId!,
            requestData
        );
        if (invoicesData && invoicesData.length > 0) {
            if (requestData.dumpToDatabase) {
                await saveInvoicesToDatabase(invoicesData, requestData.companyId, prisma);
                console.log(`✅ Successfully processed ${invoicesData.length} invoices`);
            } else {
                console.log("Data fetched but not dumped to DB (dumpToDatabase is false)");
            }
        } else {
            console.log("No invoices found to process");
        }
    } catch (error) {
        console.error("Error processing invoices request:", error);
        throw error;
    }
}

function validateRequestData(data: XeroRequestData): void {
    if (!data.companyId) {
        throw new ValidationError("companyId is required");
    }
}

async function getActiveIntegration(data: XeroRequestData, prisma: PrismaClient): Promise<Company> {
    const integration = await prisma.company.findFirst({
        where: {
            Id: data.companyId,
            ConnectionStatus: "ACTIVE",
        },
    });
    if (!integration) {
        throw new Error("Active integration not found for company");
    }
    return integration;
}

async function ensureValidToken(integration: Company): Promise<Company> {
    if (!integration.XeroAccessToken || !integration.XeroTokenExpiry) {
        throw new Error("No access token found");
    }
    const now = new Date();
    const expiry = new Date(integration.XeroTokenExpiry);
    if (expiry.getTime() - now.getTime() <= 10 * 60 * 1000) {
        console.log("Token expired or about to expire, refreshing...");
        return await refreshXeroToken(integration);
    }
    return integration;
}

const getInvoices = async (
    accessToken: string,
    tenantId: string,
    requestData: XeroRequestData
): Promise<any[]> => {
    try {
        const config = getXeroConfig();
        const url = `${config.baseUrl}/Invoices`;
        console.log(`Fetching invoices from Xero API: ${url}`);
        const params: any = {};
        if (requestData.where) params.where = requestData.where;
        if (requestData.page) params.page = requestData.page;
        if (requestData.pageSize) params.pageSize = requestData.pageSize;
        const response = await axios.get(url, {
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Xero-tenant-id': tenantId,
                'Accept': 'application/json'
            },
            params,
        });
        console.log('Xero Invoices API raw response:', JSON.stringify(response.data, null, 2));
        if (response.data && response.data.Invoices) {
            console.log(`Retrieved ${response.data.Invoices.length} invoices from Xero`);
            return response.data.Invoices;
        }
        return [];
    } catch (error: any) {
        console.error("Error fetching invoices from Xero:", error.response?.data || error.message);
        throw new XeroError(`Failed to fetch invoices: ${error.response?.data?.Elements?.[0]?.ValidationErrors?.[0]?.Message || error.message}`);
    }
};

function parseXeroDate(dateString: string | null | undefined): Date | null {
    if (!dateString || typeof dateString !== 'string') return null;
    const match = dateString.match(/\/Date\((\d+)([+-]\d{4})\)\//);
    if (match) {
        const timestamp = parseInt(match[1] || "0");
        return new Date(timestamp);
    }
    const parsedDate = new Date(dateString);
    return isNaN(parsedDate.getTime()) ? null : parsedDate;
}

function mapXeroInvoiceToPrismaInvoice(invoice: any, companyId: string): any {
    return {
        InvoiceID: invoice.InvoiceID,
        Type: invoice.Type || null,
        InvoiceNumber: invoice.InvoiceNumber || null,
        ContactID: invoice.Contact?.ContactID || null,
        ContactName: invoice.Contact?.Name || null,
        FirstName: invoice.Contact?.FirstName || null,
        LastName: invoice.Contact?.LastName || null,
        Date: parseXeroDate(invoice.Date),
        DueDate: parseXeroDate(invoice.DueDate),
        FullyPaidOnDate: parseXeroDate(invoice.FullyPaidOnDate),
        ExpectedPaymentDate: parseXeroDate(invoice.ExpectedPaymentDate),
        PlannedPaymentDate: parseXeroDate(invoice.PlannedPaymentDate),
        LineAmountTypes: invoice.LineAmountTypes || null,
        Reference: invoice.Reference || null,
        Status: invoice.Status || null,
        HasAttachments: invoice.HasAttachments ?? null,
        SentToContact: invoice.SentToContact ?? null,
        BrandingThemeID: invoice.BrandingThemeID || null,
        BrandingThemeName: invoice.BrandingThemeName || null,
        AmountDue: invoice.AmountDue || null,
        AmountPaid: invoice.AmountPaid || null,
        AmountCredited: invoice.AmountCredited || null,
        AmountPaidToDate: invoice.AmountPaidToDate || null,
        AmountDueToDate: invoice.AmountDueToDate || null,
        AmountCreditedToDate: invoice.AmountCreditedToDate || null,
        CurrencyCode: invoice.CurrencyCode || null,
        CurrencyRate: invoice.CurrencyRate || null,
        SubTotal: invoice.SubTotal || null,
        TotalDiscount: invoice.TotalDiscount || null,
        TotalTax: invoice.TotalTax || null,
        Total: invoice.Total || null,
        UpdateUTCDate: parseXeroDate(invoice.UpdatedDateUTC) || new Date(),
        CompanyId: companyId
    };
}

function mapXeroInvoiceLineToPrismaInvoiceLine(line: any, invoice: any, companyId: string): any {
    return {
        Id: line.LineItemID || undefined,
        InvoiceID: invoice.InvoiceID,
        InvoiceNumber: invoice.InvoiceNumber || null,
        ContactID: invoice.Contact?.ContactID || null,
        ContactName: invoice.Contact?.Name || null,
        Date: parseXeroDate(invoice.Date),
        Reference: invoice.Reference || null,
        LineItemID: line.LineItemID || null,
        ItemCode: line.ItemCode || null,
        Description: line.Description || null,
        Quantity: line.Quantity || null,
        UnitAmount: line.UnitAmount || null,
        UnitCost: line.UnitCost || null,
        DiscountRate: line.DiscountRate || null,
        AccountCode: line.AccountCode || null,
        AccountID: line.AccountID || null,
        TaxAmount: line.TaxAmount || null,
        TaxType: line.TaxType || null,
        LineAmountTypes: line.LineAmountTypes || null,
        LineAmount: line.LineAmount || null,
        LineAmountIncl: line.LineAmountIncl || null,
        LineAmountExcl: line.LineAmountExcl || null,
        CompanyId: companyId,
        TrackingCategory1: (line.Tracking && line.Tracking[0]?.Name) || null,
        TrackingCategory1Value: (line.Tracking && line.Tracking[0]?.Option) || null,
        TrackingCategory2: (line.Tracking && line.Tracking[1]?.Name) || null,
        TrackingCategory2Value: (line.Tracking && line.Tracking[1]?.Option) || null,
        CurrencyCode: invoice.CurrencyCode || null,
        CurrencyRate: invoice.CurrencyRate || null,
    };
}

async function saveInvoicesToDatabase(
    invoices: any[],
    companyId: string,
    prisma: PrismaClient
): Promise<void> {
    console.log(`Saving ${invoices.length} invoices to database`);

    // --- INVOICES ---
    // 1. Map and collect all invoices
    const invoiceRecords = invoices.map(inv => mapXeroInvoiceToPrismaInvoice(inv, companyId));
    const invoiceIds = invoiceRecords.map(inv => inv.InvoiceID);

    // 2. Fetch existing invoice IDs
    const existingInvoices = await prisma.invoice.findMany({
        where: { InvoiceID: { in: invoiceIds } },
        select: { InvoiceID: true }
    });
    const existingInvoiceIds = new Set(existingInvoices.map(n => n.InvoiceID));

    // 3. Split into new and existing
    const invoicesToInsert = invoiceRecords.filter(inv => !existingInvoiceIds.has(inv.InvoiceID));
    const invoicesToUpdate = invoiceRecords.filter(inv => existingInvoiceIds.has(inv.InvoiceID));

    // 4. Bulk insert new invoices
    if (invoicesToInsert.length > 0) {
        await prisma.invoice.createMany({ data: invoicesToInsert, skipDuplicates: true });
        console.log(`Inserted ${invoicesToInsert.length} new invoices`);
    }
    // 5. Bulk update existing invoices
    if (invoicesToUpdate.length > 0) {
        await prisma.$transaction(
            invoicesToUpdate.map(invoice =>
                prisma.invoice.update({
                    where: { InvoiceID: invoice.InvoiceID },
                    data: invoice
                })
            )
        );
        console.log(`Updated ${invoicesToUpdate.length} existing invoices`);
    }

    // --- INVOICE LINES ---
    // 1. Map and collect all invoice lines
    const allLines = invoices.flatMap(inv =>
        (Array.isArray(inv.LineItems) ? inv.LineItems.map((line: any) => mapXeroInvoiceLineToPrismaInvoiceLine(line, inv, companyId)) : [])
    );
    const lineIds = allLines.map(line => line.Id).filter(Boolean);

    // 1a. Delete extra invoice lines for these invoices
    if (invoiceIds.length > 0) {
        await prisma.invoiceLine.deleteMany({
            where: {
                InvoiceID: { in: invoiceIds },
                Id: { notIn: lineIds },
            },
        });
        console.log(`Deleted invoice lines not present in latest Xero data for these invoices.`);
    }

    // 2. Fetch existing invoice line IDs
    const existingLines = await prisma.invoiceLine.findMany({
        where: { Id: { in: lineIds } },
        select: { Id: true }
    });
    const existingLineIds = new Set(existingLines.map(l => l.Id));

    // 3. Split into new and existing
    const linesToInsert = allLines.filter((line: any) => line.Id && !existingLineIds.has(line.Id));
    const linesToUpdate = allLines.filter((line: any) => line.Id && existingLineIds.has(line.Id));

    // 4. Bulk insert new invoice lines
    if (linesToInsert.length > 0) {
        await prisma.invoiceLine.createMany({ data: linesToInsert, skipDuplicates: true });
        console.log(`Inserted ${linesToInsert.length} new invoice lines`);
    }
    // 5. Bulk update existing invoice lines
    if (linesToUpdate.length > 0) {
        await prisma.$transaction(
            linesToUpdate.map(line =>
                prisma.invoiceLine.update({
                    where: { Id: line.Id },
                    data: line
                })
            )
        );
        console.log(`Updated ${linesToUpdate.length} existing invoice lines`);
    }
} 