{"D:\\Tushar\\project\\Assiduous\\AssiduousXeroIntegrationLambda\\employees\\serverless.yml": {"versionFramework": "4.17.1", "servicePath": "D:\\Tushar\\project\\Assiduous\\AssiduousXeroIntegrationLambda\\employees\\serverless.yml", "serviceConfigFileName": "serverless.yml", "service": {"service": "xero-employees", "frameworkVersion": "4", "provider": {"name": "aws", "runtime": "nodejs18.x", "region": "us-east-1", "memorySize": 1024, "architecture": "x86_64", "tracing": {"lambda": true}, "timeout": 500, "environment": {"DATABASE_URL": "postgresql://postgres:<EMAIL>:5432/furge?schema=public", "XERO_CLIENT_ID": "DB9419E9BC6D4B989B90B960B2EFB2A3", "XERO_CLIENT_SECRET": "<REDACTED>", "XERO_TOKEN_URL": "<REDACTED>", "XERO_BASE_URL": "https://api.xero.com/api.xro/2.0/", "FIRST_RETRY": "10", "SECOND_RETRY": "20", "LAST_RETRY": "30", "REGION": "us-east-1", "ACCESS_KEY_ID": "<REDACTED>", "SECRET_ACCESS_KEY": "<REDACTED>", "IS_OFFLINE": "true", "PRISMA_QUERY_ENGINE_LIBRARY": "/var/task/node_modules/.prisma/client/libquery_engine-rhel-openssl-1.0.x.so.node"}, "stage": "dev", "versionFunctions": true}, "functions": {"xeroHandler": {"handler": "src/handlers/xeroEmployeesHandler.handler", "environment": {"IS_OFFLINE": "true"}, "events": [{"sqs": {"arn": {"Fn::GetAtt": ["EmployeesSyncQueue", "<PERSON><PERSON>"]}, "batchSize": 5}}, {"http": {"path": "xero/sync-employees", "method": "post"}}]}}, "package": {"patterns": ["!node_modules/.prisma/client/libquery_engine-darwin*", "!node_modules/.prisma/client/libquery_engine-windows*", "!node_modules/.prisma/client/libquery_engine-arm*", "!node_modules/.prisma/client/libquery_engine-debian*", "!node_modules/.prisma/client/libquery_engine-rhel-openssl-3.0.x*", "!node_modules/@prisma/engines/**", "node_modules/.prisma/**", "src/**", "node_modules/**", "!node_modules/prisma/**"]}, "plugins": ["serverless-offline", "serverless-offline-sqs"], "resources": {"Resources": {"EmployeesSyncQueue": {"Type": "AWS::SQS::Queue", "Properties": {"QueueName": "employees-sync-queue", "VisibilityTimeoutSeconds": 600, "MessageRetentionPeriod": 1209600, "ReceiveMessageWaitTimeSeconds": 20, "RedrivePolicy": {"deadLetterTargetArn": {"Fn::GetAtt": ["EmployeesSyncDLQ", "<PERSON><PERSON>"]}, "maxReceiveCount": 3}}}, "EmployeesSyncDLQ": {"Type": "AWS::SQS::Queue", "Properties": {"QueueName": "employees-sync-dlq", "MessageRetentionPeriod": 1209600}}}}, "custom": {"serverless-offline-sqs": {"autoCreate": true, "apiVersion": "2012-11-05", "endpoint": "http://0.0.0.0:9324", "region": "us-east-1", "accessKeyId": "<REDACTED>", "secretAccessKey": "<REDACTED>", "skipCacheInvalidation": false}}}, "provider": {"name": "aws", "runtime": "nodejs18.x", "region": "us-east-1", "memorySize": 1024, "architecture": "x86_64", "tracing": {"lambda": true}, "timeout": 500, "environment": {"DATABASE_URL": "postgresql://postgres:<EMAIL>:5432/furge?schema=public", "XERO_CLIENT_ID": "DB9419E9BC6D4B989B90B960B2EFB2A3", "XERO_CLIENT_SECRET": "<REDACTED>", "XERO_TOKEN_URL": "<REDACTED>", "XERO_BASE_URL": "https://api.xero.com/api.xro/2.0/", "FIRST_RETRY": "10", "SECOND_RETRY": "20", "LAST_RETRY": "30", "REGION": "us-east-1", "ACCESS_KEY_ID": "<REDACTED>", "SECRET_ACCESS_KEY": "<REDACTED>", "IS_OFFLINE": "true", "PRISMA_QUERY_ENGINE_LIBRARY": "/var/task/node_modules/.prisma/client/libquery_engine-rhel-openssl-1.0.x.so.node"}, "stage": "dev", "versionFunctions": true}, "dashboard": {"isEnabledForService": false, "requiredAuthentication": false, "orgFeaturesInUse": null, "orgObservabilityIntegrations": null, "serviceAppId": null, "serviceProvider": null, "instanceParameters": null}, "error": {"message": "Cannot find module '@serverless/utils/log'\nRequire stack:\n- D:\\Tushar\\project\\Assiduous\\AssiduousXeroIntegrationLambda\\employees\\node_modules\\serverless-offline-sqs\\src\\index.js", "stack": "Error: Cannot find module '@serverless/utils/log'\nRequire stack:\n- D:\\Tushar\\project\\Assiduous\\AssiduousXeroIntegrationLambda\\employees\\node_modules\\serverless-offline-sqs\\src\\index.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1143:15)\n    at Module._load (node:internal/modules/cjs/loader:984:27)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (D:\\Tushar\\project\\Assiduous\\AssiduousXeroIntegrationLambda\\employees\\node_modules\\serverless-offline-sqs\\src\\index.js:15:13)\n    at Module._compile (node:internal/modules/cjs/loader:1369:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)\n    at Module.load (node:internal/modules/cjs/loader:1206:32)\n    at Module._load (node:internal/modules/cjs/loader:1022:12)\n    at cjsLoader (node:internal/modules/esm/translators:366:17)", "code": "MODULE_NOT_FOUND"}, "serviceRawFile": "service: xero-employees\n\nframeworkVersion: '4'\n\nprovider:\n  name: aws\n  runtime: nodejs18.x\n  region: us-east-1\n  memorySize: 1024\n  architecture: x86_64\n  tracing:\n    lambda: true\n  timeout: 500\n  environment:\n    DATABASE_URL: ${env:DATABASE_URL}\n    XERO_CLIENT_ID: ${env:XERO_CLIENT_ID}\n    XERO_CLIENT_SECRET: ${env:XERO_CLIENT_SECRET}\n    XERO_TOKEN_URL: ${env:XERO_TOKEN_URL}\n    XERO_BASE_URL: ${env:XERO_BASE_URL}\n    FIRST_RETRY: ${env:FIRST_RETRY}\n    SECOND_RETRY: ${env:SECOND_RETRY}\n    LAST_RETRY: ${env:LAST_RETRY}\n    REGION: ${env:REGION}\n    ACCESS_KEY_ID: ${env:ACCESS_KEY_ID}\n    SECRET_ACCESS_KEY: ${env:SECRET_ACCESS_KEY}\n    IS_OFFLINE: ${env:IS_OFFLINE, 'false'}\n    PRISMA_QUERY_ENGINE_LIBRARY: /var/task/node_modules/.prisma/client/libquery_engine-rhel-openssl-1.0.x.so.node\n\nfunctions:\n  xeroHandler:\n    handler: src/handlers/xeroEmployeesHandler.handler\n    environment:\n      IS_OFFLINE: ${env:IS_OFFLINE, 'true'}\n    events:\n      - sqs:\n          arn:\n            Fn::GetAtt: [EmployeesSyncQueue, Arn]\n          batchSize: 5\n      - http:\n          path: xero/sync-employees\n          method: post\n\npackage:\n  patterns:\n    - '!node_modules/.prisma/client/libquery_engine-darwin*'\n    - '!node_modules/.prisma/client/libquery_engine-windows*'\n    - '!node_modules/.prisma/client/libquery_engine-arm*'\n    - '!node_modules/.prisma/client/libquery_engine-debian*'\n    - '!node_modules/.prisma/client/libquery_engine-rhel-openssl-3.0.x*'\n    - '!node_modules/@prisma/engines/**'\n    - 'node_modules/.prisma/**'\n    - 'src/**'\n    - 'node_modules/**'\n    - '!node_modules/prisma/**'\n\nplugins:\n  - serverless-offline\n  - serverless-offline-sqs\n\nresources:\n  Resources:\n    EmployeesSyncQueue:\n      Type: AWS::SQS::Queue\n      Properties:\n        QueueName: employees-sync-queue\n        VisibilityTimeoutSeconds: 600\n        MessageRetentionPeriod: 1209600\n        ReceiveMessageWaitTimeSeconds: 20\n        RedrivePolicy:\n          deadLetterTargetArn:\n            Fn::GetAtt: [EmployeesSyncDLQ, Arn]\n          maxReceiveCount: 3\n\n    EmployeesSyncDLQ:\n      Type: AWS::SQS::Queue\n      Properties:\n        QueueName: employees-sync-dlq\n        MessageRetentionPeriod: 1209600\n\ncustom:\n  serverless-offline-sqs:\n    autoCreate: true\n    apiVersion: '2012-11-05'\n    endpoint: http://0.0.0.0:9324\n    region: us-east-1\n    accessKeyId: root\n    secretAccessKey: root\n    skipCacheInvalidation: false\n", "command": ["offline"], "options": {}, "orgId": "********-05ed-463b-9c61-6a42b9e0a0fb", "orgName": "testor<PERSON>us", "userId": "75cWDV7wPcW0B3jgnf", "userName": "testor<PERSON>us", "serviceProviderAwsAccountId": "************", "serviceProviderAwsCfStackId": null, "serviceProviderAwsCfStackCreated": null, "serviceProviderAwsCfStackUpdated": null, "serviceProviderAwsCfStackStatus": null, "serviceProviderAwsCfStackOutputs": null}}