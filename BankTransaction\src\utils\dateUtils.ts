/**
 * Date Utilities for Xero API Integration
 *
 * This utility provides date formatting and parsing functions specifically
 * for Xero API integration, particularly for the UpdatedDateUTC parameter.
 *
 * Key Features:
 * - Format dates for Xero API UpdatedDateUTC parameter
 * - Parse Xero's .NET date format
 * - Handle timezone conversions
 * - Provide default sync dates for initial sync
 *
 * <AUTHOR> Date Utilities
 * @version 1.0.0
 */

/**
 * Format a Date object to Xero API UpdatedDateUTC format
 * Format: DateTime(YYYY,M,dTHH:mm:ss)
 * Example: DateTime(2025,7,1T00:00:00)
 */
export function formatDateForXeroAPI(date: Date): string {
  const year = date.getFullYear();
  const month = date.getMonth() + 1; // JavaScript months are 0-based
  const day = date.getDate();
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  const seconds = date.getSeconds().toString().padStart(2, '0');

  return `DateTime(${year},${month},${day}T${hours}:${minutes}:${seconds})`;
}

/**
 * Parse Xero's .NET date format to JavaScript Date
 * Handles formats like: "/Date(1751362260967+0000)/"
 */
export function parseXeroDate(dateString: string | null | undefined): Date | null {
  if (!dateString || typeof dateString !== 'string') return null;

  // Handle Xero's .NET date format: "/Date(1751362260967+0000)/"
  const match = dateString.match(/\/Date\((\d+)([+-]\d{4})\)\//);
  if (match) {
    const timestamp = parseInt(match[1] || '0');
    return new Date(timestamp);
  }

  // Try parsing as regular date string
  const parsedDate = new Date(dateString);
  return isNaN(parsedDate.getTime()) ? null : parsedDate;
}

/**
 * Get default sync start date (30 days ago)
 * Used for initial sync when no previous sync date exists
 */
export function getDefaultSyncStartDate(): Date {
  const date = new Date();
  date.setDate(date.getDate() - 30); // 30 days ago
  date.setHours(0, 0, 0, 0); // Start of day
  return date;
}

/**
 * Get current UTC date for sync timestamp
 */
export function getCurrentSyncDate(): Date {
  return new Date();
}

/**
 * Format date for logging purposes
 */
export function formatDateForLogging(date: Date): string | null {

  return date === null ? null : date.toISOString();
}

/**
 * Check if a date is valid
 */
export function isValidDate(date: any): date is Date {
  return date instanceof Date && !isNaN(date.getTime());
}

/**
 * Create a date from ISO string with error handling
 */
export function safeDateFromISO(isoString: string | null | undefined): Date | null {
  if (!isoString) return null;

  try {
    const date = new Date(isoString);
    return isValidDate(date) ? date : null;
  } catch {
    return null;
  }
}

/**
 * Get the start of day for a given date
 */
export function getStartOfDay(date: Date): Date {
  const startOfDay = new Date(date);
  startOfDay.setHours(0, 0, 0, 0);
  return startOfDay;
}

/**
 * Get the end of day for a given date
 */
export function getEndOfDay(date: Date): Date {
  const endOfDay = new Date(date);
  endOfDay.setHours(23, 59, 59, 999);
  return endOfDay;
}

/**
 * Calculate the difference in days between two dates
 */
export function getDaysDifference(startDate: Date, endDate: Date): number {
  const timeDifference = endDate.getTime() - startDate.getTime();
  return Math.ceil(timeDifference / (1000 * 3600 * 24));
}

/**
 * Add days to a date
 */
export function addDays(date: Date, days: number): Date {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
}

/**
 * Subtract days from a date
 */
export function subtractDays(date: Date, days: number): Date {
  const result = new Date(date);
  result.setDate(result.getDate() - days);
  return result;
}

/**
 * Generates a Xero-compatible DateTime expression.
 * @param {Date} [date=new Date()] - The date to convert. Defaults to current UTC date and time.
 * @returns {string} - The formatted DateTime expression.
 */
export const generateXeroDateTime = (date = new Date()) => {
  const year = date.getUTCFullYear();
  const month = date.getUTCMonth() + 1; // JavaScript months are 0-based; Xero expects 1-based.
  const day = date.getUTCDate();
  const hour = date.getUTCHours();
  const minute = date.getUTCMinutes();
  const second = date.getUTCSeconds();

  return `DateTime(${year}, ${month}, ${day}, ${hour}, ${minute}, ${second})`;
}
