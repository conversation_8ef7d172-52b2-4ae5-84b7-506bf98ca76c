import axios from '../utils/axiosInstance';
import { PrismaClient, Company } from '@prisma/client';
import { getXeroConfig } from '../config/environment';
import { XeroTokenResponse, TokenRefreshError } from '../types';

const prisma = new PrismaClient();

export async function refreshXeroToken(integration: Company): Promise<Company> {
  const { tokenUrl, clientId, clientSecret } = getXeroConfig();

  const basicAuth = Buffer.from(`${clientId}:${clientSecret}`).toString('base64');

  const payload = new URLSearchParams({
    grant_type: 'refresh_token',
    refresh_token: integration.XeroRefreshToken!, // assume this is stored in DB
  });

  try {
    const res = await axios.post<XeroTokenResponse>(tokenUrl, payload.toString(), {
      headers: {
        Authorization: `Basic ${basicAuth}`,
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });
    const { access_token, refresh_token, expires_in } = res.data;

    const updatedCompany = await prisma.company.update({
      where: { Id: integration.Id },
      data: {
        XeroAccessToken: access_token,
        XeroRefreshToken: refresh_token,
        XeroTokenExpiry: new Date(Date.now() + expires_in * 1000),
        XeroRefreshTokenExpiry: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000),
      },
    });

    return updatedCompany;
  } catch (error: any) {
    console.error('Token refresh failed:', error?.response?.data || error.message);
    throw new TokenRefreshError('Failed to refresh Xero token', error);
  }
}
