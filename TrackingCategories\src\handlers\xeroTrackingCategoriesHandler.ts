/*
 * Xero Tracking Categories Data Synchronization Handler
 *
 * This AWS Lambda handler synchronizes Tracking Category and Option data from Xero API.
 *
 * Key Features:
 * - Bulk insert and update of tracking categories and their options
 * - Handles both API Gateway and SQS events
 * - Automatic Xero token refresh
 * - Comprehensive error handling and logging
 *
 * Usage:
 * - API Gateway: POST /trackingcategories/sync with { companyId: "uuid" }
 * - SQS: Queue message with { companyId: "uuid", tenantId?: "string" }
 *
 * Environment Variables Required:
 * - DATABASE_URL: PostgreSQL connection string
 * - XERO_CLIENT_ID: Xero OAuth client ID
 * - XERO_CLIENT_SECRET: Xero OAuth client secret
 * - XERO_REDIRECT_URI: Xero OAuth redirect URI
 * - XERO_BASE_URL: Xero API base URL (optional, defaults to production)
 */
import { APIGatewayProxyEvent, Context, APIGatewayProxyResult, SQSEvent } from 'aws-lambda';
import { PrismaClient, Company } from '@prisma/client';
import axiosInstance from '../utils/axiosInstance';
import { refreshXeroToken } from '../services/refreshTokenService';
import { getXeroConfig } from '../config/environment';
import {
    XeroRequestData,
    ValidationError,
    XeroError,
} from '../types';
import dotenv from 'dotenv';
dotenv.config();
import {
    startTrackingCategoriesExecution,
    logTrackingCategoriesSuccess,
    logTrackingCategoriesFailure,
    TrackingCategoriesExecutionResult,
} from '../utils/trackingCategoriesLogger';
import {
    getCurrentSyncDate,
    formatDateForLogging
} from '../utils/dateUtils';

// Prisma
let prisma: PrismaClient | null = null;
function getPrismaClient(): PrismaClient {
    if (!prisma) {
        const config = {
            log: ['error', 'warn'] as Array<'error' | 'warn'>,
            errorFormat: 'pretty' as const,
        };
        if (process.env['IS_OFFLINE'] === 'true') {
            delete process.env['PRISMA_QUERY_ENGINE_LIBRARY'];
        }
        prisma = new PrismaClient(config);
    }
    return prisma;
}

// Lambda handler
export const handler = async (
    event: APIGatewayProxyEvent | SQSEvent,
    context: Context
): Promise<APIGatewayProxyResult | void> => {
    if ("Records" in event && event.Records?.length) {
        // SQS
        for (const record of event.Records) {
            try {
                const requestData = JSON.parse(record.body);
                await processRequestWithLogging(requestData, context, 'SYSTEM');
            } catch (err) {
                console.error("Failed to process SQS message", err);
                throw err; // let Lambda/SQS retry
            }
        }
        return;
    } else {
        // API Gateway
        try {
            const requestData = parseRequestData(event);
            await processRequestWithLogging(requestData, context, 'USER');
            return {
                statusCode: 200,
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({
                    success: true,
                    message: "Tracking Category data processed successfully",
                    timestamp: new Date().toISOString(),
                }),
            };
        } catch (err: any) {
            console.error("Handler error:", err);
            return {
                statusCode: err instanceof ValidationError ? 400 : 500,
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({
                    success: false,
                    error: err.message,
                    type: err.constructor.name,
                }),
            };
        }
    }
};

// Wrapper for processRequest with logging
async function processRequestWithLogging(
    requestData: XeroRequestData,
    context: Context,
    triggeredBy: 'USER' | 'SYSTEM' = 'SYSTEM'
): Promise<void> {
    const startTime = Date.now();
    let integrationLogId: string | null = null;
    let processedTrackingCategories = 0;
    let insertedTrackingCategories = 0;
    let updatedTrackingCategories = 0;
    let totalTrackingCategories = 0;
    try {
        // Start execution logging
        const { logId } = await startTrackingCategoriesExecution(requestData.companyId, triggeredBy);
        integrationLogId = logId;
        // Validate and process
        validateRequestData(requestData);
        const prisma = getPrismaClient();
        const integration = await getActiveIntegration(requestData, prisma);
        if (!integration) {
            throw new Error("Active integration not found");
        }
        const validIntegration = await ensureValidToken(integration);
        // Fetch tracking categories from Xero
        const trackingCategoriesData = await getTrackingCategories(
            validIntegration.XeroAccessToken!,
            validIntegration.XeroTenantId!,
            requestData
        );
        totalTrackingCategories = trackingCategoriesData.length;
        if (trackingCategoriesData && trackingCategoriesData.length > 0) {
            if (requestData.dumpToDatabase) {
                // Save to DB and count inserted/updated
                const { inserted, updated } = await saveTrackingCategoriesToDatabaseWithCounts(trackingCategoriesData, requestData.companyId, prisma);
                processedTrackingCategories = trackingCategoriesData.length;
                insertedTrackingCategories = inserted;
                updatedTrackingCategories = updated;
                console.log(`✅ Successfully processed ${processedTrackingCategories} tracking categories (${insertedTrackingCategories} inserted, ${updatedTrackingCategories} updated)`);
            } else {
                processedTrackingCategories = trackingCategoriesData.length;
                console.log("Data fetched but not dumped to DB (dumpToDatabase is false)");
            }
        } else {
            console.log("No tracking categories found to process");
        }
        // Log success
        if (integrationLogId) {
            const executionResult: TrackingCategoriesExecutionResult = {
                totalTrackingCategories: totalTrackingCategories,
                processedTrackingCategories: processedTrackingCategories,
                insertedTrackingCategories: insertedTrackingCategories,
                updatedTrackingCategories: updatedTrackingCategories,
                errors: 0,
                warnings: 0,
                duration: `${Date.now() - startTime}ms`,
            };
            await logTrackingCategoriesSuccess(integrationLogId, startTime, executionResult);
            // Update last sync date for incremental sync
            await updateLastSyncDate(requestData.companyId, getCurrentSyncDate(), prisma);
        }
        const totalDuration = Date.now() - startTime;
        console.log(`✅ Tracking Categories sync completed successfully in ${(totalDuration / 1000).toFixed(1)}s`);
    } catch (error: any) {
        // Log failure
        if (integrationLogId) {
            await logTrackingCategoriesFailure(integrationLogId, startTime, error, {
                totalTrackingCategories,
                processedTrackingCategories: processedTrackingCategories,
                insertedTrackingCategories: insertedTrackingCategories,
                updatedTrackingCategories: updatedTrackingCategories,
                errors: 1,
                warnings: 0,
            });
        }
        const totalDuration = Date.now() - startTime;
        console.error(`❌ Tracking Categories sync failed after ${(totalDuration / 1000).toFixed(1)}s:`, {
            error: error.message,
            companyId: requestData.companyId,
        });
        throw error;
    }
}

// parse event
export function parseRequestData(
    event: APIGatewayProxyEvent | SQSEvent | { body?: string }
): XeroRequestData {
    // handle SQS
    if ("Records" in event && event.Records?.[0]) {
        try {
            const parsed = JSON.parse(event.Records[0].body);
            return parsed as XeroRequestData;
        } catch {
            throw new ValidationError("Failed to parse SQS message body");
        }
    }

    // handle API Gateway
    if ("body" in event && event.body) {
        try {
            const parsed =
                typeof event.body === "string" ? JSON.parse(event.body) : event.body;
            return parsed as XeroRequestData;
        } catch {
            throw new ValidationError("Failed to parse API Gateway body");
        }
    }

    throw new ValidationError("Invalid request data");
}

// Validate request data
function validateRequestData(data: XeroRequestData): void {
    if (!data.companyId) {
        throw new ValidationError("companyId is required");
    }
}

// Get active integration
async function getActiveIntegration(data: XeroRequestData, prisma: PrismaClient): Promise<Company> {
    const integration = await prisma.company.findFirst({
        where: {
            Id: data.companyId,
            ConnectionStatus: "ACTIVE",
        },
    });

    if (!integration) {
        throw new Error("Active integration not found for company");
    }

    return integration;
}

// Ensure valid token
async function ensureValidToken(integration: Company): Promise<Company> {
    if (!integration.XeroAccessToken || !integration.XeroTokenExpiry) {
        throw new Error("No access token found");
    }

    const now = new Date();
    const expiry = new Date(integration.XeroTokenExpiry);
    // Add a 10 minute buffer (600,000 ms)
    if (expiry.getTime() - now.getTime() <= 10 * 60 * 1000) {
        console.log("Token expired or about to expire, refreshing...");
        return await refreshXeroToken(integration);
    }

    return integration;
}

// Get tracking categories from Xero
const getTrackingCategories = async (
  accessToken: string,
  tenantId: string,
  requestData: XeroRequestData
): Promise<any[]> => {
  try {
    const config = getXeroConfig();
    const url = `${config.baseUrl}/TrackingCategories`;
    const params = requestData.where ? { where: requestData.where } : {};
    const response = await axiosInstance.get(url, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        'Xero-tenant-id': tenantId,
        Accept: 'application/json',
      },
      params,
    });
    console.log('Xero TrackingCategories API raw response:', JSON.stringify(response.data, null, 2));
    if (response.data && response.data.TrackingCategories) {
      console.log(`Retrieved ${response.data.TrackingCategories.length} tracking categories from Xero`);
      return response.data.TrackingCategories;
    }
    return [];
  } catch (error: any) {
    console.error("Error fetching tracking categories from Xero:", error.response?.data || error.message);
    throw new XeroError(`Failed to fetch tracking categories: ${error.response?.data?.Elements?.[0]?.ValidationErrors?.[0]?.Message || error.message}`);
  }
};

function mapXeroTrackingCategoryToPrisma(trackingCategory: any, companyId: string) {
  return {
    TrackingCategoryID: trackingCategory.TrackingCategoryID,
    CategoryName: trackingCategory.Name, // Correct field for Prisma
    Status: trackingCategory.Status,
    TotalOptions: trackingCategory.Options ? trackingCategory.Options.length : 0,
    UpdateUTCDate: getCurrentSyncDate(),
    CompanyId: companyId,
  };
}

function mapXeroTrackingOptionToPrisma(trackingCategoryId: string, option: any, companyId: string) {
  return {
    TrackingCategoryID: trackingCategoryId,
    TrackingOptionID: option.TrackingOptionID,
    Name: option.Name,
    Status: option.Status,
    IsRecentUpdatedInTable: true,
    CompanyId: companyId,
  };
}
 
// Save tracking categories to database and return inserted/updated counts
async function saveTrackingCategoriesToDatabaseWithCounts(
    trackingCategories: any[],
    companyId: string,
    prisma: PrismaClient
): Promise<{ inserted: number; updated: number }> {
    // Step 1: Map all Xero tracking categories to Prisma TrackingCategory records
    const trackingCategoryRecords = trackingCategories.map(tc => mapXeroTrackingCategoryToPrisma(tc, companyId));
    // Step 2: Fetch all existing TrackingCategories for this company
    const existingTrackingCategories = await prisma.trackingCategory.findMany({ where: { CompanyId: companyId } });
    const existingTrackingCategoryIds = new Set(existingTrackingCategories.map(tc => tc.TrackingCategoryID));
    // Step 3: Split into new and existing tracking categories
    const categoriesToInsert = trackingCategoryRecords.filter(tc => !existingTrackingCategoryIds.has(tc.TrackingCategoryID));
    const categoriesToUpdate = trackingCategoryRecords.filter(tc => existingTrackingCategoryIds.has(tc.TrackingCategoryID));
    // Step 4: Bulk insert new tracking categories
    console.log("Insert",categoriesToInsert.length)
    console.log("Update",categoriesToUpdate.length)
    if (categoriesToInsert.length > 0) {
        await prisma.trackingCategory.createMany({ data: categoriesToInsert, skipDuplicates: true });
    }
    // Step 5: Bulk update existing tracking categories (use transaction)
    if (categoriesToUpdate.length > 0) {
        await prisma.$transaction(
            categoriesToUpdate.map(tc =>
                prisma.trackingCategory.updateMany({ where: { TrackingCategoryID: tc.TrackingCategoryID, CompanyId: companyId }, data: tc })
            )
        );
    }
    // Step 6: Prepare all tracking category options (lines)
    let allOptionRecords: any[] = [];
    for (const trackingCategory of trackingCategories) {
        if (trackingCategory.Options && Array.isArray(trackingCategory.Options)) {
            for (const option of trackingCategory.Options) {
                allOptionRecords.push(mapXeroTrackingOptionToPrisma(trackingCategory.TrackingCategoryID, option, companyId));
            }
        }
    }
    // Step 7: Fetch all existing TrackingCategoryLines for these categories and company only
    const fetchedTrackingCategoryIdsArr = trackingCategories.map(tc => tc.TrackingCategoryID);
    const existingLines = await prisma.trackingCategoryLine.findMany({
        where: {
            TrackingCategoryID: { in: fetchedTrackingCategoryIdsArr },
            CompanyId: companyId,
        },
    });
    const existingLineKeys = new Set(existingLines.map(l => `${l.TrackingCategoryID}||${l.TrackingOptionID}`));
    // Step 8: Split into new and existing lines
    const linesToInsert = allOptionRecords.filter(l => !existingLineKeys.has(`${l.TrackingCategoryID}||${l.TrackingOptionID}`));
    const linesToUpdate = allOptionRecords.filter(l => existingLineKeys.has(`${l.TrackingCategoryID}||${l.TrackingOptionID}`));
    // Step 9: Delete extra TrackingCategoryLines not present in latest Xero data for fetched categories only
    const fetchedTrackingCategoryIds = new Set(trackingCategories.map(tc => tc.TrackingCategoryID));
    const xeroLineKeys = new Set(allOptionRecords.map(l => `${l.TrackingCategoryID}||${l.TrackingOptionID}`));
    const existingLinesForFetched = existingLines.filter(l => fetchedTrackingCategoryIds.has(l.TrackingCategoryID));
    const linesToDelete = existingLinesForFetched.filter(
        l => !xeroLineKeys.has(`${l.TrackingCategoryID}||${l.TrackingOptionID}`)
    );
    if (linesToDelete.length > 0) {
        await prisma.trackingCategoryLine.deleteMany({
            where: {
                OR: linesToDelete.map(l => ({
                    TrackingCategoryID: l.TrackingCategoryID,
                    TrackingOptionID: l.TrackingOptionID,
                    CompanyId: companyId,
                })),
            },
        });
    }
    // Step 10: Bulk insert new lines
    if (linesToInsert.length > 0) {
        await prisma.trackingCategoryLine.createMany({ data: linesToInsert, skipDuplicates: true });
    }
    // Step 11: Bulk update existing lines (use transaction)
    if (linesToUpdate.length > 0) {
        await prisma.$transaction(
            linesToUpdate.map(l =>
                prisma.trackingCategoryLine.updateMany({
                    where: { TrackingCategoryID: l.TrackingCategoryID, TrackingOptionID: l.TrackingOptionID, CompanyId: companyId },
                    data: l,
                })
            )
        );
    }
    // Return inserted/updated counts
    return { inserted: categoriesToInsert.length, updated: categoriesToUpdate.length };
}

// Update last sync date in xeroModuleSync table
async function updateLastSyncDate(companyId: string, syncDate: Date, prisma: PrismaClient): Promise<void> {
    try {
        await prisma.xeroModuleSync.upsert({
            where: {
                CompanyId_ModuleName: {
                    CompanyId: companyId,
                    ModuleName: 'Tracking Categories'
                }
            },
            update: {
                LastSyncTime: syncDate,
                UpdatedAt: getCurrentSyncDate()
            },
            create: {
                Id: require('uuid').v4(),
                CompanyId: companyId,
                ModuleName: 'Tracking Categories',
                LastSyncTime: syncDate,
                CreatedAt: getCurrentSyncDate(),
                UpdatedAt: getCurrentSyncDate()
            }
        });
        console.log(`📅 Updated last sync date to: ${formatDateForLogging(syncDate)}`);
    } catch (error) {
        console.error("Error updating last sync date:", error);
        // Don't throw here as this shouldn't fail the main sync process
    }
}

