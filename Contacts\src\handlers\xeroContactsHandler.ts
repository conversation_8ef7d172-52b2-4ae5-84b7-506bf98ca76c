/**
 * Xero Contacts Data Synchronization Handler
 *
 * This AWS Lambda handler synchronizes Contacts data from Xero API.
 *
 * Key Features:
 * - Bulk insert and update of contacts
 * - Handles both API Gateway and SQS events
 * - Automatic Xero token refresh
 * - Comprehensive error handling and logging
 *
 * Usage:
 * - API Gateway: POST /contacts/sync with { companyId: "uuid" }
 * - SQS: Queue message with { companyId: "uuid", tenantId?: "string" }
 *
 * Environment Variables Required:
 * - DATABASE_URL: PostgreSQL connection string
 * - XERO_CLIENT_ID: Xero OAuth client ID
 * - XERO_CLIENT_SECRET: Xero OAuth client secret
 * - XERO_REDIRECT_URI: Xero OAuth redirect URI
 * - XERO_BASE_URL: Xero API base URL (optional, defaults to production)
 */
import { APIGatewayProxyEvent, Context, APIGatewayProxyResult, SQSEvent } from 'aws-lambda';
import { PrismaClient, Company, Account } from '@prisma/client';

import axios from '../utils/axiosInstance';
import { refreshXeroToken } from '../services/refreshTokenService';
import { getXeroConfig } from '../config/environment';
import {
    XeroRequestData,
    ValidationError,
    XeroError,
} from '../types';

import dotenv from 'dotenv';
dotenv.config();

// Prisma
let prisma: PrismaClient | null = null;
function getPrismaClient(): PrismaClient {
    if (!prisma) {
        const config = {
            log: ['error', 'warn'] as Array<'error' | 'warn'>,
            errorFormat: 'pretty' as const,
        };
        if (process.env.IS_OFFLINE === 'true') {
            delete process.env.PRISMA_QUERY_ENGINE_LIBRARY;
        }
        prisma = new PrismaClient(config);
    }
    return prisma;
}

// Lambda handler
export const handler = async (
    event: APIGatewayProxyEvent | SQSEvent,
    context: Context
): Promise<APIGatewayProxyResult | void> => {
    if ("Records" in event && event.Records?.length) {
        // SQS
        for (const record of event.Records) {
            try {
                const requestData = JSON.parse(record.body);
                await processRequest(requestData, context);
            } catch (err) {
                console.error("Failed to process SQS message", err);
                throw err; // let Lambda/SQS retry
            }
        }
        return;
    } else {
        // API Gateway
        try {
            const requestData = parseRequestData(event);
            await processRequest(requestData, context);

            return {
                statusCode: 200,
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({
                    success: true,
                    message: "Contacts data processed successfully",
                    timestamp: new Date().toISOString(),
                }),
            };
        } catch (err: any) {
            console.error("Handler error:", err);
            return {
                statusCode: err instanceof ValidationError ? 400 : 500,
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({
                    success: false,
                    error: err.message,
                    type: err.constructor.name,
                }),
            };
        }
    }
};

// parse event
export function parseRequestData(
    event: APIGatewayProxyEvent | SQSEvent | { body?: string }
): XeroRequestData {
    // handle SQS
    if ("Records" in event && event.Records?.[0]) {
        try {
            const parsed = JSON.parse(event.Records[0].body);
            return parsed as XeroRequestData;
        } catch {
            throw new ValidationError("Failed to parse SQS message body");
        }
    }

    // handle API Gateway
    if ("body" in event && event.body) {
        try {
            const parsed =
                typeof event.body === "string" ? JSON.parse(event.body) : event.body;
            return parsed as XeroRequestData;
        } catch {
            throw new ValidationError("Failed to parse API Gateway body");
        }
    }

    throw new ValidationError("Invalid request data");
}

// process request
async function processRequest(
    requestData: XeroRequestData,
    _context: Context
): Promise<void> {
    try {
        validateRequestData(requestData);

        const prisma = getPrismaClient();
        const integration = await getActiveIntegration(requestData, prisma);

        if (!integration) {
            throw new Error("Active integration not found");
        }

        const validIntegration = await ensureValidToken(integration);

        console.log(`🚀 Fetching Contacts for company: ${requestData.companyId}`);

        // Fetch contacts from Xero
        const contactsData = await getContacts(
            validIntegration.XeroAccessToken!,
            validIntegration.XeroTenantId!,
            requestData
        );

        if (contactsData && contactsData.length > 0) {
            if (requestData.dumpToDatabase) {
                await saveContactsToDatabase(contactsData, requestData.companyId, prisma);
                console.log(`✅ Successfully processed ${contactsData.length} contacts`);
            } else {
                console.log("Data fetched but not dumped to DB (dumpToDatabase is false)");
            }
        } else {
            console.log("No contacts found to process");
        }

    } catch (error) {
        console.error("Error processing contacts request:", error);
        throw error;
    }
}

// Validate request data
function validateRequestData(data: XeroRequestData): void {
    if (!data.companyId) {
        throw new ValidationError("companyId is required");
    }
}

// Get active integration
async function getActiveIntegration(data: XeroRequestData, prisma: PrismaClient): Promise<Company> {
    const integration = await prisma.company.findFirst({
        where: {
            Id: data.companyId,
            ConnectionStatus: "ACTIVE",
        },
    });

    if (!integration) {
        throw new Error("Active integration not found for company");
    }

    return integration;
}

// Ensure valid token
async function ensureValidToken(integration: Company): Promise<Company> {
    if (!integration.XeroAccessToken || !integration.XeroTokenExpiry) {
        throw new Error("No access token found");
    }

    const now = new Date();
    const expiry = new Date(integration.XeroTokenExpiry);
    // Add a 10 minute buffer (600,000 ms)
    if (expiry.getTime() - now.getTime() <= 10 * 60 * 1000) {
        console.log("Token expired or about to expire, refreshing...");
        return await refreshXeroToken(integration);
    }

    return integration;
}

// Get contacts from Xero
const getContacts = async (
    accessToken: string,
    tenantId: string,
    requestData: XeroRequestData
): Promise<any[]> => {
    try {
        const config = getXeroConfig();
        const url = `${config.baseUrl}/Contacts`;

        console.log(`Fetching contacts from Xero API: ${url}`);

        // Build query params
        const params: any = {};
        if (requestData.where) params.where = requestData.where;
        if (requestData.page) params.page = requestData.page;
        if (requestData.pageSize) params.pageSize = requestData.pageSize;

        const response = await axios.get(url, {
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Xero-tenant-id': tenantId,
                'Accept': 'application/json'
            },
            params,
        });

        // Log the full Xero response for debugging
        console.log('Xero Contacts API raw response:', JSON.stringify(response.data, null, 2));

        if (response.data && response.data.Contacts) {
            console.log(`Retrieved ${response.data.Contacts.length} contacts from Xero`);
            return response.data.Contacts;
        }

        return [];
    } catch (error: any) {
        console.error("Error fetching contacts from Xero:", error.response?.data || error.message);
        throw new XeroError(`Failed to fetch contacts: ${error.response?.data?.Elements?.[0]?.ValidationErrors?.[0]?.Message || error.message}`);
    }
};

// Helper function to parse Xero's .NET date format
function parseXeroDate(dateString: string | null | undefined): Date | null {
    if (!dateString || typeof dateString !== 'string') return null;

    // Handle Xero's .NET date format: "/Date(1751362260967+0000)/"
    const match = dateString.match(/\/Date\((\d+)([+-]\d{4})\)\//);
    if (match) {
        const timestamp = parseInt(match[1] || "0");
        return new Date(timestamp);
    }

    // Try parsing as regular date string
    const parsedDate = new Date(dateString);
    return isNaN(parsedDate.getTime()) ? null : parsedDate;
}

// Map Xero contact to Prisma contact data
function mapXeroContactToPrismaContact(contact: any, companyId: string): any {
    // Extract phone numbers
    let phoneDefault: any = {}, phoneDDI: any = {}, phoneMobile: any = {}, phoneFax: any = {};
    if (Array.isArray(contact.Phones)) {
        for (const phone of contact.Phones) {
            switch (phone.PhoneType) {
                case 'DEFAULT': phoneDefault = phone; break;
                case 'DDI': phoneDDI = phone; break;
                case 'MOBILE': phoneMobile = phone; break;
                case 'FAX': phoneFax = phone; break;
            }
        }
    }

    // Extract addresses
    let mailingAddress: any = {}, streetAddress: any = {};
    if (Array.isArray(contact.Addresses)) {
        for (const address of contact.Addresses) {
            if (address.AddressType === 'POBOX') {
                mailingAddress = address;
            } else if (address.AddressType === 'STREET') {
                streetAddress = address;
            }
        }
    }

    // Extract contact groups
    let contactGroups = null;
    if (Array.isArray(contact.ContactGroups) && contact.ContactGroups.length > 0) {
        contactGroups = contact.ContactGroups.map((g: any) => g.Name).filter(Boolean).join(',');
    }

    // Extract batch payments
    const batchPayments: any = contact.BatchPayments || {};

    // Extract balances
    const ar: any = contact.Balances?.AccountsReceivable || {};
    const ap: any = contact.Balances?.AccountsPayable || {};

    return {
        ContactID: contact.ContactID,
        ContactNumber: contact.ContactNumber || null,
        ContactStatus: contact.ContactStatus || null,
        AccountNumber: contact.AccountNumber || null,
        Name: contact.Name || null,
        FirstName: contact.FirstName || null,
        LastName: contact.LastName || null,
        EmailAddress: contact.EmailAddress || null,
        SkypeUserName: contact.SkypeUserName || null,
        DefaultCurrency: contact.DefaultCurrency || null,
        BankAccountDetails: contact.BankAccountDetails || null,
        TaxNumber: contact.TaxNumber || null,
        AccountsReceivableTaxType: contact.AccountsReceivableTaxType || null,
        AccountsPayableTaxType: contact.AccountsPayableTaxType || null,
        IsSupplier: contact.IsSupplier || false,
        IsCustomer: contact.IsCustomer || false,
        PurchasesDefaultAccountCode: contact.PurchasesDefaultAccountCode || null,
        SalesDefaultAccountCode: contact.SalesDefaultAccountCode || null,
        BatchPaymentsBankAccountNumber: batchPayments.BankAccountNumber || null,
        BatchPaymentsBankAccountName: batchPayments.BankAccountName || null,
        BatchPaymentsDetails: batchPayments.Details || null,
        AccountsReceivableOutstanding: ar.Outstanding || 0,
        AccountsReceivableOverdue: ar.Overdue || 0,
        AccountsPayableOutstanding: ap.Outstanding || 0,
        AccountsPayableOverdue1: ap.Overdue || 0,
        PhoneDefaultNumber: phoneDefault.PhoneNumber || null,
        PhoneDefaultAreaCode: phoneDefault.PhoneAreaCode || null,
        PhoneDefaultCountryCode: phoneDefault.PhoneCountryCode || null,
        PhoneDDINumber: phoneDDI.PhoneNumber || null,
        PhoneDDIAreaCode: phoneDDI.PhoneAreaCode || null,
        PhoneDDICountryCode: phoneDDI.PhoneCountryCode || null,
        PhoneMobileNumber: phoneMobile.PhoneNumber || null,
        PhoneMobileAreaCode: phoneMobile.PhoneAreaCode || null,
        PhoneMobileCountryCode: phoneMobile.PhoneCountryCode || null,
        PhoneFaxNumber: phoneFax.PhoneNumber || null,
        PhoneFaxAreaCode: phoneFax.PhoneAreaCode || null,
        PhoneFaxCountryCode: phoneFax.PhoneCountryCode || null,
        MailingAddressAttensionTo: mailingAddress.AttentionTo || null,
        MailingAddressAddressLine1: mailingAddress.AddressLine1 || null,
        MailingAddressAddressLine2: mailingAddress.AddressLine2 || null,
        MailingAddressAddressLine3: mailingAddress.AddressLine3 || null,
        MailingAddressAddressLine4: mailingAddress.AddressLine4 || null,
        MailingAddressCity: mailingAddress.City || null,
        MailingAddressCountry: mailingAddress.Country || null,
        MailingAddressPostalCode: mailingAddress.PostalCode || null,
        MailingAddressRegion: mailingAddress.Region || null,
        StreetAttensionTo: streetAddress.AttentionTo || null,
        StreetAddressLine1: streetAddress.AddressLine1 || null,
        StreetAddressLine2: streetAddress.AddressLine2 || null,
        StreetAddressLine3: streetAddress.AddressLine3 || null,
        StreetAddressLine4: streetAddress.AddressLine4 || null,
        StreetCity: streetAddress.City || null,
        StreetCountry: streetAddress.Country || null,
        StreetPostalCode: streetAddress.PostalCode || null,
        StreetRegion: streetAddress.Region || null,
        ContactGroups: contactGroups,
        UpdateUTCDate: parseXeroDate(contact.UpdatedDateUTC)?.toISOString() || new Date().toISOString(),
        CompanyId: companyId
    };
}

// Save contacts to database
async function saveContactsToDatabase(
    contacts: any[],
    companyId: string,
    prisma: PrismaClient
): Promise<void> {
    console.log(`Saving ${contacts.length} contacts to database`);

    // 1. Map all contacts to DB shape
    const contactRecords = contacts.map(contact => mapXeroContactToPrismaContact(contact, companyId));
    const contactIds = contactRecords.map(c => c.ContactID);

    // 2. Fetch existing ContactIDs
    const existingContacts = await prisma.contact.findMany({
        where: { ContactID: { in: contactIds } },
        select: { ContactID: true }
    });
    const existingContactIds = new Set(existingContacts.map(c => c.ContactID));

    // 3. Split into new and existing
    const contactsToInsert = contactRecords.filter(c => !existingContactIds.has(c.ContactID));
    const contactsToUpdate = contactRecords.filter(c => existingContactIds.has(c.ContactID));

    // 4. Bulk insert new contacts
    if (contactsToInsert.length > 0) {
        await prisma.contact.createMany({ data: contactsToInsert, skipDuplicates: true });
        console.log(`Inserted ${contactsToInsert.length} new contacts`);
    }
    // 5. Bulk update existing contacts
    if (contactsToUpdate.length > 0) {
        await prisma.$transaction(
            contactsToUpdate.map(contact =>
                prisma.contact.update({
                    where: { ContactID: contact.ContactID },
                    data: contact
                })
            )
        );
        console.log(`Updated ${contactsToUpdate.length} existing contacts`);
    }
}
